#!/usr/bin/env python3
"""
Debug script for Enhanced Bybit Client
Tests and debugs the enhanced Bybit client functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient


async def debug_enhanced_client():
    """Debug the enhanced Bybit client"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugEnhancedClient")
    
    logger.info("Starting Enhanced Bybit Client Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize enhanced client
        client = EnhancedBybitClient(config)
        logger.info("Enhanced Bybit Client initialized")
        
        # Test connection
        logger.info("Testing API connection...")
        server_time = await client.get_server_time()
        logger.info(f"Server time: {server_time}")
        
        # Test account info
        logger.info("Testing account information...")
        account_info = await client.get_account_info()
        logger.info(f"Account info: {account_info}")
        
        # Test market data
        logger.info("Testing market data...")
        symbols = await client.get_symbols()
        logger.info(f"Available symbols: {len(symbols) if symbols else 0}")
        
        if symbols:
            # Test ticker data for first symbol
            symbol = symbols[0]['symbol'] if isinstance(symbols, list) else 'BTCUSDT'
            ticker = await client.get_ticker(symbol)
            logger.info(f"Ticker for {symbol}: {ticker}")
            
            # Test orderbook
            orderbook = await client.get_order_book(symbol)
            logger.info(f"Orderbook for {symbol}: {len(orderbook.get('bids', []))} bids, {len(orderbook.get('asks', []))} asks")
        
        # Test enhanced features
        logger.info("Testing enhanced features...")
        
        # Test rate limiting
        logger.info("Testing rate limiting...")
        for i in range(5):
            await client.get_server_time()
            logger.info(f"Rate limit test {i+1}/5 completed")
        
        # Test error handling
        logger.info("Testing error handling...")
        try:
            await client.get_ticker("INVALID_SYMBOL")
        except Exception as e:
            logger.info(f"Error handling test successful: {e}")
        
        logger.info("Enhanced Bybit Client debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'client' in locals():
            await client.close()
            logger.info("Client connection closed")


if __name__ == "__main__":
    print("Enhanced Bybit Client Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_enhanced_client())
    
    print("=" * 50)
    print("Debug session completed")
