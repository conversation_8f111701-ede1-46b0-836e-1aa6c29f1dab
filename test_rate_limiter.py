#!/usr/bin/env python3
"""
Test script for the global rate limiter
"""

import asyncio
import time
import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from bybit_bot.utils.global_rate_limiter import rate_limiter

async def test_rate_limiter():
    """Test the rate limiter functionality"""
    print("Testing rate limiter...")
    
    # Test normal operation
    print("Testing normal rate limiting...")
    for i in range(3):
        start_time = time.time()
        await rate_limiter.acquire("normal")
        end_time = time.time()
        print(f"Request {i+1}: Waited {end_time - start_time:.2f} seconds")
        rate_limiter.report_success()
    
    # Test error reporting
    print("\nTesting error reporting...")
    rate_limiter.report_error("Test error")
    rate_limiter.report_error("Access too frequent")
    
    # Test 5-minute ban simulation
    print("\nTesting 5-minute ban simulation...")
    rate_limiter.report_error("Access too frequent. Please try again in 5 minutes.")
    
    print("Rate limiter test completed successfully!")

if __name__ == "__main__":
    asyncio.run(test_rate_limiter())
