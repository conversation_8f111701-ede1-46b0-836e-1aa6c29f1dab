#!/usr/bin/env python3
"""
Test Bybit Symbol Availability
Check which symbols are available in different categories
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_bybit_symbols():
    """Test symbol availability across different categories"""
    print(f"[{datetime.now()}] Testing Bybit symbol availability...")
    
    base_url = "https://api.bybit.com"
    
    async with aiohttp.ClientSession() as session:
        # Test spot category
        print("\n[SPOT] Testing spot symbols...")
        try:
            url = f"{base_url}/v5/market/instruments-info?category=spot&limit=10"
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('result') and data['result'].get('list'):
                        print(f"[SUCCESS] Found {len(data['result']['list'])} spot symbols")
                        for symbol in data['result']['list'][:5]:
                            print(f"  - {symbol['symbol']} (status: {symbol.get('status', 'unknown')})")
                    else:
                        print("[ERROR] No spot symbols found")
                else:
                    print(f"[ERROR] Spot request failed: {response.status}")
        except Exception as e:
            print(f"[ERROR] Spot test failed: {e}")
        
        # Test linear category (derivatives)
        print("\n[LINEAR] Testing linear derivatives...")
        try:
            url = f"{base_url}/v5/market/instruments-info?category=linear&limit=10"
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('result') and data['result'].get('list'):
                        print(f"[SUCCESS] Found {len(data['result']['list'])} linear symbols")
                        for symbol in data['result']['list'][:5]:
                            print(f"  - {symbol['symbol']} (status: {symbol.get('status', 'unknown')})")
                    else:
                        print("[ERROR] No linear symbols found")
                else:
                    print(f"[ERROR] Linear request failed: {response.status}")
        except Exception as e:
            print(f"[ERROR] Linear test failed: {e}")
        
        # Test specific symbols
        print("\n[SPECIFIC] Testing specific symbols...")
        test_symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT"]
        
        for symbol in test_symbols:
            print(f"\nTesting {symbol}:")
            
            # Test in spot
            try:
                url = f"{base_url}/v5/market/tickers?category=spot&symbol={symbol}"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('result') and data['result'].get('list'):
                            price = data['result']['list'][0]['lastPrice']
                            print(f"  [SPOT] Available - Price: ${price}")
                        else:
                            print(f"  [SPOT] Not found")
                    else:
                        print(f"  [SPOT] Error: {response.status}")
            except Exception as e:
                print(f"  [SPOT] Error: {e}")
            
            # Test in linear
            try:
                url = f"{base_url}/v5/market/tickers?category=linear&symbol={symbol}"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('result') and data['result'].get('list'):
                            price = data['result']['list'][0]['lastPrice']
                            print(f"  [LINEAR] Available - Price: ${price}")
                        else:
                            print(f"  [LINEAR] Not found")
                    else:
                        print(f"  [LINEAR] Error: {response.status}")
            except Exception as e:
                print(f"  [LINEAR] Error: {e}")
        
        # Test klines specifically
        print("\n[KLINES] Testing klines endpoint...")
        try:
            url = f"{base_url}/v5/market/kline?category=linear&symbol=BTCUSDT&interval=1&limit=5"
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('result') and data['result'].get('list'):
                        print(f"[SUCCESS] Klines available for BTCUSDT linear")
                        print(f"  Latest price: {data['result']['list'][0][4]}")
                    else:
                        print("[ERROR] No klines data")
                else:
                    print(f"[ERROR] Klines request failed: {response.status}")
                    error_data = await response.json()
                    print(f"  Error details: {error_data}")
        except Exception as e:
            print(f"[ERROR] Klines test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_bybit_symbols())
