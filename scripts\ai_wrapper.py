#!/usr/bin/env python3
'''
AI Command Wrapper - Prevents hanging and optimizes execution
'''
import subprocess
import sys
import os
import time

def execute_ai_command():
    if len(sys.argv) < 2:
        print("Usage: ai_wrapper.py <command>")
        sys.exit(1)
    
    command = " ".join(sys.argv[1:])
    
    # Set environment
    env = os.environ.copy()
    env.update({
        'CONDA_DEFAULT_ENV': 'bybit-trader',
        'CONDA_PREFIX': 'E:/conda/miniconda3/envs/bybit-trader',
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8'
    })
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=r'E:\The_real_deal_copy\Bybit_Bot\BOT',
            env=env
        )
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr, file=sys.stderr)
        
        sys.exit(result.returncode)
        
    except subprocess.TimeoutExpired:
        print("Command timed out after 30 seconds", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Command execution failed: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    execute_ai_command()
