#!/usr/bin/env python3
"""
Simple import test to check for circular imports or other issues
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("Testing imports...")

try:
    print("1. Importing BotConfig...")
    from bybit_bot.core.config import BotConfig
    print("SUCCESS: BotConfig imported")
    
    print("2. Importing DatabaseManager...")
    from bybit_bot.database.connection import DatabaseManager
    print("SUCCESS: DatabaseManager imported")
    
    print("3. Importing PersistentMemoryManager...")
    from bybit_bot.ai.memory_manager import PersistentMemoryManager
    print("SUCCESS: PersistentMemoryManager imported")
    
    print("4. Creating instances...")
    config = BotConfig()
    print("SUCCESS: BotConfig instance created")
    
    db_manager = DatabaseManager(config)
    print("SUCCESS: DatabaseManager instance created")
    
    print("5. Checking DatabaseManager methods...")
    methods = ['fetch_all', 'fetch_one', 'execute_sql']
    for method in methods:
        if hasattr(db_manager, method):
            print(f"SUCCESS: {method} exists")
        else:
            print(f"ERROR: {method} missing")
    
    print("6. Creating PersistentMemoryManager...")
    memory_manager = PersistentMemoryManager(config, db_manager)
    print("SUCCESS: PersistentMemoryManager instance created")
    
    print("7. Checking memory_manager.db...")
    print(f"Type: {type(memory_manager.db)}")
    print(f"Has fetch_all: {hasattr(memory_manager.db, 'fetch_all')}")
    
    print("ALL TESTS PASSED!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
