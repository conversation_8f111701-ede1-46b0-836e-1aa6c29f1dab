#!/usr/bin/env python3
"""
Test script to isolate database import issues
"""

import sys
import traceback

def test_database_import():
    """Test importing the database manager"""
    try:
        print("Testing database import...")
        from bybit_bot.database.connection import DatabaseManager
        print("SUCCESS: DatabaseManager imported successfully")
        return True
    except Exception as e:
        print(f"ERROR: Failed to import DatabaseManager: {e}")
        traceback.print_exc()
        return False

def test_config_import():
    """Test importing the config"""
    try:
        print("Testing config import...")
        from bybit_bot.core.config import BotConfig
        print("SUCCESS: BotConfig imported successfully")
        return True
    except Exception as e:
        print(f"ERROR: Failed to import BotConfig: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("ISOLATED DATABASE IMPORT TEST")
    print("=" * 50)
    
    # Test config first
    config_success = test_config_import()
    print()
    
    # Test database import
    db_success = test_database_import()
    print()
    
    if config_success and db_success:
        print("ALL IMPORTS SUCCESSFUL")
        sys.exit(0)
    else:
        print("IMPORT FAILURES DETECTED")
        sys.exit(1)
