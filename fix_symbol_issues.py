#!/usr/bin/env python3
"""
Fix Symbol Issues - Check and correct Bybit symbol formats
"""

import asyncio
import json
from datetime import datetime

try:
    from bybit_bot.core.config import ConfigManager
    from bybit_bot.core.enhanced_bybit_client import EnhancedBybitClient
    print("SUCCESS: Imports loaded")
except Exception as e:
    print(f"IMPORT ERROR: {e}")
    exit(1)

async def main():
    print("=== BYBIT SYMBOL VALIDATION FIX ===")
    print(f"Time: {datetime.now()}")
    
    try:
        # Load config
        config_manager = ConfigManager()
        config = config_manager.get_config()
        print("SUCCESS: Config loaded")
        
        # Check testnet setting
        testnet = config['bybit'].get('testnet', False)
        print(f"Testnet mode: {testnet}")
        print(f"API endpoint will be: {'testnet' if testnet else 'mainnet'}")
        
        # Create client
        client = EnhancedBybitClient(
            api_key=config['bybit']['api_key'],
            api_secret=config['bybit']['api_secret'],
            testnet=testnet
        )
        print("SUCCESS: Client created")
        
        # 1. Get available instruments/symbols
        print("\n=== CHECKING AVAILABLE SYMBOLS ===")
        try:
            # Check spot symbols
            instruments = await client.get_instruments_info(category="spot")
            print(f"Instruments response: {json.dumps(instruments, indent=2)}")
            
            if instruments and 'result' in instruments:
                symbols = []
                for item in instruments['result']['list']:
                    symbol = item.get('symbol', '')
                    if 'USDT' in symbol and item.get('status') == 'Trading':
                        symbols.append(symbol)
                
                print(f"\nFound {len(symbols)} active USDT trading pairs:")
                for i, symbol in enumerate(symbols[:20]):  # Show first 20
                    print(f"  {i+1}. {symbol}")
                
                # Test a few symbols
                test_symbols = symbols[:5] if symbols else ['BTCUSDT', 'ETHUSDT']
                print(f"\n=== TESTING SYMBOLS ===")
                
                for symbol in test_symbols:
                    try:
                        print(f"\nTesting {symbol}:")
                        
                        # Test klines
                        klines = await client.get_klines(
                            category="spot",
                            symbol=symbol,
                            interval="1",
                            limit=5
                        )
                        print(f"  Klines: {'SUCCESS' if klines.get('retCode') == 0 else 'FAILED'}")
                        if klines.get('retCode') != 0:
                            print(f"    Error: {klines}")
                        
                        # Test ticker
                        ticker = await client.get_tickers(category="spot", symbol=symbol)
                        print(f"  Ticker: {'SUCCESS' if ticker.get('retCode') == 0 else 'FAILED'}")
                        if ticker.get('retCode') != 0:
                            print(f"    Error: {ticker}")
                            
                    except Exception as e:
                        print(f"  Error testing {symbol}: {e}")
            
        except Exception as e:
            print(f"Error getting instruments: {e}")
        
        # 2. Test account access
        print("\n=== TESTING ACCOUNT ACCESS ===")
        try:
            balance = await client.get_wallet_balance(accountType="UNIFIED")
            print(f"Balance check: {'SUCCESS' if balance.get('retCode') == 0 else 'FAILED'}")
            if balance.get('retCode') != 0:
                print(f"Balance error: {balance}")
            else:
                print("Account access working correctly")
        except Exception as e:
            print(f"Account access error: {e}")
        
        # 3. Check server time
        print("\n=== TESTING SERVER CONNECTION ===")
        try:
            server_time = await client.get_server_time()
            print(f"Server time: {'SUCCESS' if server_time.get('retCode') == 0 else 'FAILED'}")
            if server_time.get('retCode') == 0:
                print(f"Server timestamp: {server_time.get('time')}")
        except Exception as e:
            print(f"Server time error: {e}")
        
        await client.close()
        print("\nSUCCESS: Symbol validation completed")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
