#!/usr/bin/env python3
"""
VS Code Extension Host Troubleshooting Script
Fixes common extension host failures and performance issues
"""

import os
import subprocess
import json
import shutil
import time
from pathlib import Path
import psutil

class ExtensionHostFixer:
    def __init__(self):
        self.vscode_data_dir = Path(os.path.expandvars("%APPDATA%/Code - Insiders"))
        self.user_data_dir = self.vscode_data_dir / "User"
        self.extensions_dir = Path(os.path.expandvars("%USERPROFILE%/.vscode-insiders/extensions"))
        
    def print_status(self, message):
        """Print status message without emojis"""
        print(f"[INFO] {message}")
        
    def print_error(self, message):
        """Print error message without emojis"""
        print(f"[ERROR] {message}")
        
    def print_success(self, message):
        """Print success message without emojis"""
        print(f"[OK] {message}")
        
    def kill_vscode_processes(self):
        """Kill all VS Code processes to ensure clean restart"""
        self.print_status("Killing VS Code processes...")
        
        processes_to_kill = [
            "Code - Insiders.exe",
            "Code.exe", 
            "electron.exe"
        ]
        
        killed_count = 0
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] in processes_to_kill:
                    proc.kill()
                    killed_count += 1
                    self.print_status(f"Killed process: {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
                
        if killed_count > 0:
            self.print_success(f"Killed {killed_count} VS Code processes")
            time.sleep(3)  # Wait for processes to fully terminate
        else:
            self.print_status("No VS Code processes found running")
    
    def clear_extension_cache(self):
        """Clear VS Code extension cache and logs"""
        self.print_status("Clearing extension cache and logs...")
        
        cache_dirs = [
            self.vscode_data_dir / "logs",
            self.vscode_data_dir / "CachedExtensions",
            self.vscode_data_dir / "CachedExtensionVSIXs",
            self.user_data_dir / "workspaceStorage",
            self.user_data_dir / "globalStorage"
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    shutil.rmtree(cache_dir)
                    self.print_success(f"Cleared: {cache_dir}")
                except Exception as e:
                    self.print_error(f"Failed to clear {cache_dir}: {e}")
    
    def backup_and_reset_settings(self):
        """Backup current settings and create minimal config"""
        self.print_status("Backing up and resetting VS Code settings...")
        
        settings_file = self.user_data_dir / "settings.json"
        if settings_file.exists():
            backup_file = settings_file.with_suffix(".json.backup")
            shutil.copy2(settings_file, backup_file)
            self.print_success(f"Settings backed up to: {backup_file}")
        
        # Create minimal settings to reduce extension host load
        minimal_settings = {
            "python.defaultInterpreterPath": "E:/anaconda3/envs/autogpt-trader/python.exe",
            "python.terminal.activateEnvironment": True,
            "python.analysis.autoImportCompletions": False,
            "python.analysis.indexing": False,
            "extensions.autoUpdate": False,
            "extensions.autoCheckUpdates": False,
            "workbench.enableExperiments": False,
            "telemetry.telemetryLevel": "off",
            "workbench.reduceMotion": "on",
            "editor.minimap.enabled": False,
            "editor.renderWhitespace": "none",
            "editor.codeLens": False,
            "git.enabled": False,
            "terminal.integrated.rendererType": "dom"
        }
        
        try:
            with open(settings_file, 'w') as f:
                json.dump(minimal_settings, f, indent=2)
            self.print_success("Created minimal settings configuration")
        except Exception as e:
            self.print_error(f"Failed to create minimal settings: {e}")
    
    def disable_problematic_extensions(self):
        """Disable commonly problematic extensions"""
        self.print_status("Checking for problematic extensions...")
        
        problematic_extensions = [
            "ms-python.pylint",
            "ms-python.flake8", 
            "ms-python.mypy-type-checker",
            "ms-toolsai.jupyter-keymap",
            "ms-vscode.vscode-json"
        ]
        
        for ext in problematic_extensions:
            try:
                result = subprocess.run(
                    ["code-insiders", "--disable-extension", ext],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if result.returncode == 0:
                    self.print_success(f"Disabled extension: {ext}")
                else:
                    self.print_status(f"Extension {ext} not found or already disabled")
            except Exception as e:
                self.print_error(f"Failed to disable {ext}: {e}")
    
    def check_system_resources(self):
        """Check system resources that might affect extension host"""
        self.print_status("Checking system resources...")
        
        # Memory check
        memory = psutil.virtual_memory()
        self.print_status(f"Available memory: {memory.available / (1024**3):.1f} GB / {memory.total / (1024**3):.1f} GB")
        
        if memory.percent > 80:
            self.print_error("High memory usage detected - this may cause extension host failures")
        
        # Disk space check
        disk = psutil.disk_usage('C:')
        free_gb = disk.free / (1024**3)
        self.print_status(f"Free disk space: {free_gb:.1f} GB")
        
        if free_gb < 5:
            self.print_error("Low disk space detected - this may cause extension host failures")
    
    def reinstall_python_extension(self):
        """Reinstall Python extension as it's commonly problematic"""
        self.print_status("Reinstalling Python extension...")
        
        try:
            # Uninstall Python extension
            subprocess.run(
                ["code-insiders", "--uninstall-extension", "ms-python.python"],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            time.sleep(5)
            
            # Reinstall Python extension
            result = subprocess.run(
                ["code-insiders", "--install-extension", "ms-python.python"],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                self.print_success("Python extension reinstalled successfully")
            else:
                self.print_error(f"Failed to reinstall Python extension: {result.stderr}")
                
        except Exception as e:
            self.print_error(f"Failed to reinstall Python extension: {e}")
    
    def create_launch_script(self):
        """Create optimized VS Code launch script"""
        self.print_status("Creating optimized VS Code launch script...")
        
        script_content = '''@echo off
echo [INFO] Starting VS Code with optimized settings...

REM Set environment variables for better performance
set ELECTRON_ENABLE_LOGGING=1
set VSCODE_LOGS=debug
set ELECTRON_NO_ATTACH_CONSOLE=1

REM Launch VS Code with performance flags
"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\Code - Insiders.exe" ^
    --max-memory=4096 ^
    --disable-extensions ^
    --disable-gpu ^
    --no-sandbox ^
    --disable-dev-shm-usage ^
    --disable-background-timer-throttling ^
    --disable-renderer-backgrounding ^
    --disable-backgrounding-occluded-windows ^
    "E:\\The_real_deal_copy\\Bybit_Bot\\BOT"

echo [INFO] VS Code launched with performance optimizations
pause
'''
        
        script_path = Path("E:/The_real_deal_copy/Bybit_Bot/BOT/launch_vscode_optimized.bat")
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)
            self.print_success(f"Created launch script: {script_path}")
        except Exception as e:
            self.print_error(f"Failed to create launch script: {e}")
    
    def run_full_fix(self):
        """Run complete extension host fix procedure"""
        print("=" * 60)
        print("VS CODE EXTENSION HOST TROUBLESHOOTING")
        print("=" * 60)
        
        self.check_system_resources()
        self.kill_vscode_processes()
        self.clear_extension_cache()
        self.backup_and_reset_settings()
        self.disable_problematic_extensions()
        self.reinstall_python_extension()
        self.create_launch_script()
        
        print("\n" + "=" * 60)
        print("EXTENSION HOST FIX COMPLETED")
        print("=" * 60)
        print("\nNext steps:")
        print("1. Use the optimized launch script: launch_vscode_optimized.bat")
        print("2. If issues persist, start VS Code with --disable-extensions flag")
        print("3. Enable extensions one by one to identify problematic ones")
        print("4. Consider using VS Code stable instead of Insiders")
        print("5. Check Windows Event Viewer for detailed error logs")

if __name__ == "__main__":
    fixer = ExtensionHostFixer()
    fixer.run_full_fix()
