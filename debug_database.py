#!/usr/bin/env python3
"""
Debug script for Database Connection
Tests and debugs the database connectivity and operations
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger, setup_logging
from bybit_bot.database.connection import DatabaseManager


async def debug_database():
    """Debug the database connection and operations"""
    
    # Setup logging
    setup_logging(log_level="DEBUG", console_output=True)
    logger = TradingBotLogger("DebugDatabase")
    
    logger.info("Starting Database Debug Session")
    
    try:
        # Initialize configuration
        config = BotConfig()
        logger.info("Configuration loaded successfully")
        
        # Initialize database manager
        db_manager = DatabaseManager(config)
        logger.info("Database Manager initialized")
        
        # Test connection
        logger.info("Testing database connection...")
        await db_manager.connect()
        logger.info("Database connected successfully")
        
        # Test basic operations
        logger.info("Testing basic database operations...")
        
        # Test simple query
        try:
            result = await db_manager.execute_sql("SELECT 1 as test_value")
            logger.info(f"Simple query result: {result}")
        except Exception as e:
            logger.warning(f"Simple query failed: {e}")
        
        # Test table creation
        logger.info("Testing table operations...")
        
        # Create test table (SQLite Compatible)
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS debug_test (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            test_data TEXT,
            test_value REAL
        )
        """
        
        try:
            await db_manager.execute_sql(create_table_sql)
            logger.info("Test table created successfully")
        except Exception as e:
            logger.warning(f"Table creation failed: {e}")
        
        # Test data insertion
        logger.info("Testing data insertion...")
        
        insert_sql = """
        INSERT INTO debug_test (test_data, test_value)
        VALUES (:test_data, :test_value) RETURNING id
        """

        try:
            test_data = [
                ("Debug test 1", 123.45),
                ("Debug test 2", 678.90),
                ("Debug test 3", 999.99)
            ]

            for data, value in test_data:
                result = await db_manager.execute_sql(insert_sql, {"test_data": data, "test_value": value})
                logger.info(f"Inserted record with ID: {result}")
        except Exception as e:
            logger.warning(f"Data insertion failed: {e}")
        
        # Test data retrieval
        logger.info("Testing data retrieval...")
        
        try:
            select_sql = "SELECT * FROM debug_test ORDER BY id DESC LIMIT 5"
            results = await db_manager.fetch_all(select_sql)
            logger.info(f"Retrieved {len(results) if results else 0} records")
            
            if results:
                for record in results:
                    logger.info(f"Record: ID={record.get('id')}, Data={record.get('test_data')}, Value={record.get('test_value')}")
        except Exception as e:
            logger.warning(f"Data retrieval failed: {e}")
        
        # Test transaction handling
        logger.info("Testing transaction handling...")
        
        try:
            # Note: transaction method may not exist, using basic execute_sql
            await db_manager.execute_sql(insert_sql, {"test_data": "Transaction test", "test_value": 555.55})
            logger.info("Transaction test data inserted")

            # Query within transaction
            count_result = await db_manager.fetch_one("SELECT COUNT(*) as count FROM debug_test")
            logger.info(f"Records in transaction: {count_result.get('count') if count_result else 0}")

            logger.info("Transaction completed successfully")
        except Exception as e:
            logger.warning(f"Transaction failed: {e}")
        
        # Test connection pooling
        logger.info("Testing connection pooling...")
        
        try:
            # Simulate multiple concurrent operations
            tasks = []
            for i in range(5):
                task = db_manager.execute_sql("SELECT pg_sleep(0.1), :task_id as task_id", {"task_id": i})
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful_tasks = sum(1 for r in results if not isinstance(r, Exception))
            logger.info(f"Concurrent operations: {successful_tasks}/5 successful")
        except Exception as e:
            logger.warning(f"Connection pooling test failed: {e}")
        
        # Test database schema operations
        logger.info("Testing schema operations...")
        
        try:
            # Check if tables exist
            table_check_sql = """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            """
            
            tables = await db_manager.fetch_all(table_check_sql)
            logger.info(f"Existing tables: {[t.get('table_name') for t in tables] if tables else []}")
        except Exception as e:
            logger.warning(f"Schema check failed: {e}")
        
        # Test performance monitoring
        logger.info("Testing performance monitoring...")
        
        try:
            # Monitor query performance
            start_time = datetime.now()
            
            performance_sql = """
            SELECT 
                COUNT(*) as total_records,
                AVG(test_value) as avg_value,
                MAX(test_value) as max_value,
                MIN(test_value) as min_value
            FROM debug_test
            """
            
            perf_result = await db_manager.fetch_one(performance_sql)
            end_time = datetime.now()
            
            query_duration = (end_time - start_time).total_seconds() * 1000  # milliseconds
            logger.info(f"Performance query completed in {query_duration:.2f}ms")
            logger.info(f"Performance results: {perf_result}")
        except Exception as e:
            logger.warning(f"Performance test failed: {e}")
        
        # Test error handling
        logger.info("Testing error handling...")
        
        try:
            # Intentionally cause an error
            await db_manager.execute_sql("SELECT * FROM non_existent_table")
        except Exception as e:
            logger.info(f"Error handling test successful: {type(e).__name__}: {e}")
        
        # Cleanup test data
        logger.info("Cleaning up test data...")
        
        try:
            cleanup_sql = "DROP TABLE IF EXISTS debug_test"
            await db_manager.execute_sql(cleanup_sql)
            logger.info("Test table cleaned up successfully")
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")
        
        logger.info("Database debug session completed successfully")
        
    except Exception as e:
        logger.error(f"Debug session failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if 'db_manager' in locals():
            await db_manager.close()
            logger.info("Database connection closed")


if __name__ == "__main__":
    print("Database Debug Session")
    print("=" * 50)
    
    # Run debug session
    asyncio.run(debug_database())
    
    print("=" * 50)
    print("Debug session completed")
