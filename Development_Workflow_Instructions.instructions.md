---
type: "agent_requested"
---

# DEVELOPMENT WORKFLOW INSTRUCTIONS

## TASK COMPLETION REQUIREMENTS
- Work until 100% success on ALL tasks
- Take next logical steps without asking confirmation
- Implement every function completely
- Keep entire system functional during changes
- Make durable fixes only, no shortcuts

## FILE ORGANIZATION
- Keep workspace clean and organized
- Files in correct folders according to structure
- Delete duplicate files immediately
- Maintain single unified system version
- Use E:\ in conda env bybit-trader

## DEPENDENCY MANAGEMENT
- Install ALL required dependencies from requirements.txt
- Use conda environment: bybit-trader
- Query official documentation for each API
- Ensure all imports are properly resolved
- Fix dependency issues immediately

## TESTING REQUIREMENTS
- All tests must mirror running main.py
- Test complete system functionality
- Verify all capabilities remain active
- Test with live data connections

## ERROR RESOLUTION
- Double-check syntax and imports
- Resolve dependency issues immediately
- Maintain system integrity during fixes
- Never compromise functionality for quick fixes

## CONTINUOUS IMPROVEMENT
- Always expand capabilities, never reduce
- Learn from trading results and backtesting
- Optimize for maximum profit
- Maintain all Super GPT functions active

## AUTONOMOUS SYSTEM REQUIREMENTS
The system must operate fully autonomously:
- Market analysis without manual intervention
- Position sizing based on risk parameters
- Order execution and management
- Portfolio rebalancing
- System monitoring and healing
- Performance optimization
- Error recovery and adaptation

## PROFIT MAXIMIZATION
All development must focus on:
- Maximum profit generation
- Risk-adjusted returns
- Efficient execution
- Minimal slippage
- Optimal position sizing
- Advanced pattern recognition
