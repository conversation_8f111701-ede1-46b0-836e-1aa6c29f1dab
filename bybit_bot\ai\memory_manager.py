"""
Persistent Memory Management System for Bybit Trading Bot
Implements self-learning capabilities and pattern recognition
"""

import asyncio
import json
import pickle
import numpy as np
import pandas as pd
import holidays
import time
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict, field
from enum import Enum
import hashlib
from collections import defaultdict, deque

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class PatternType(Enum):
    """Types of trading patterns"""
    MARKET_CONDITION = "market_condition"
    STRATEGY_PERFORMANCE = "strategy_performance"
    RISK_EVENT = "risk_event"
    PRICE_PATTERN = "price_pattern"
    VOLUME_PATTERN = "volume_pattern"
    TIME_PATTERN = "time_pattern"
    CORRELATION_PATTERN = "correlation_pattern"


class MemoryImportance(Enum):
    """Importance levels for memories"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class TimeContext:
    """Comprehensive time context for memory"""
    timestamp: datetime
    year: int
    month: int
    day: int
    hour: int
    minute: int
    second: int
    microsecond: int
    day_of_week: int  # 0=Monday, 6=Sunday
    day_of_year: int
    week_of_year: int
    quarter: int
    is_weekend: bool
    is_holiday: bool
    holiday_name: Optional[str]
    market_session: str  # 'ASIAN', 'EUROPEAN', 'US', 'OVERLAP'
    time_of_day: str  # 'MORNING', 'AFTERNOON', 'EVENING', 'NIGHT'
    timezone: str
    utc_offset: int
    time_since_market_open: Optional[timedelta]
    time_until_market_close: Optional[timedelta]


@dataclass
class StrategyState:
    """Strategy state for continuation"""
    strategy_name: str
    last_run_timestamp: datetime
    time_since_last_run: timedelta
    last_positions: Dict[str, Any]
    last_parameters: Dict[str, Any]
    last_performance: Dict[str, float]
    continuation_data: Dict[str, Any]
    state_hash: str


@dataclass
class TradingMemory:
    """Individual trading memory/experience with comprehensive time awareness"""
    memory_id: str
    pattern_type: PatternType
    importance: MemoryImportance
    timestamp: datetime
    time_context: TimeContext
    market_conditions: Dict[str, Any]
    strategy_used: str
    action_taken: str
    outcome: Dict[str, Any]
    context: Dict[str, Any]
    pattern_hash: str
    confidence: float
    usage_count: int
    last_accessed: datetime
    success_rate: float
    temporal_relevance: float
    time_decay_factor: float


@dataclass
class PatternMatch:
    """Pattern matching result"""
    memory: TradingMemory
    similarity_score: float
    confidence: float
    recommended_action: str
    risk_assessment: Dict[str, Any]


class PersistentMemoryManager:
    """
    Advanced memory management system that:
    - Stores and retrieves trading experiences
    - Recognizes similar market patterns
    - Learns from trading outcomes
    - Adapts strategy parameters based on experience
    - Maintains persistent knowledge across restarts
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.logger = TradingBotLogger(config)

        # CRITICAL FIX: Detect and handle circular reference
        if database_manager is None:
            self.logger.error("CRITICAL ERROR: database_manager is None!")
            # Create a dummy database manager to prevent crashes
            self.db = None
        elif isinstance(database_manager, PersistentMemoryManager):
            self.logger.error("CRITICAL ERROR: Circular reference detected! database_manager is PersistentMemoryManager instead of DatabaseManager")
            # Try to get the actual database manager from the circular reference
            if hasattr(database_manager, 'db') and database_manager.db is not None and not isinstance(database_manager.db, PersistentMemoryManager):
                self.logger.info("RECOVERY: Found actual DatabaseManager in circular reference")
                self.db = database_manager.db
            else:
                self.logger.error("RECOVERY FAILED: Cannot find actual DatabaseManager")
                self.db = None
        elif hasattr(database_manager, 'fetch_all'):
            self.db = database_manager
        else:
            self.logger.error(f"ERROR: database_manager missing fetch_all method. Type: {type(database_manager)}")
            self.logger.error(f"Available methods: {[m for m in dir(database_manager) if not m.startswith('_')]}")
            self.db = None
        
        # Memory storage
        self.memories: Dict[str, TradingMemory] = {}
        self.pattern_index: Dict[str, List[str]] = defaultdict(list)
        self.recent_memories: deque = deque(maxlen=1000)
        
        # Learning parameters
        self.similarity_threshold = 0.7
        self.min_confidence = 0.6
        self.max_memories = 10000
        self.memory_decay_days = 90
        
        # Pattern recognition
        self.feature_weights = {
            'volatility': 0.2,
            'volume': 0.15,
            'trend_strength': 0.2,
            'rsi': 0.1,
            'macd': 0.1,
            'time_of_day': 0.1,
            'market_session': 0.15
        }
        
        # Performance tracking
        self.pattern_performance: Dict[str, Dict[str, int]] = defaultdict(lambda: {'success': 0, 'total': 0})
        self.strategy_memory: Dict[str, Dict[str, Any]] = defaultdict(dict)

        # Time awareness
        self.strategy_states: Dict[str, StrategyState] = {}
        self.last_system_run: Optional[datetime] = None
        self.system_downtime: Optional[timedelta] = None

        # Holiday calendars for comprehensive time awareness
        self.holidays = {
            'US': holidays.country_holidays('US'),
            'UK': holidays.country_holidays('GB'),
            'JP': holidays.country_holidays('JP'),
            'EU': holidays.country_holidays('DE')
        }
        
    async def initialize(self):
        """Initialize the memory manager"""
        try:
            self.logger.info("INITIALIZING Persistent Memory Manager...")
            
            # Load existing memories from database
            await self._load_memories_from_database()
            
            # Build pattern index
            await self._build_pattern_index()
            
            # Load strategy memories
            await self._load_strategy_memories()
            
            self.logger.info(f"SUCCESS: Memory Manager initialized with {len(self.memories)} memories")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Memory Manager: {e}")
            raise
    
    async def store_trading_experience(self, 
                                     market_conditions: Dict[str, Any],
                                     strategy_used: str,
                                     action_taken: str,
                                     outcome: Dict[str, Any],
                                     context: Optional[Dict[str, Any]] = None) -> str:
        """Store a new trading experience"""
        try:
            # Create pattern hash for similarity matching
            pattern_hash = self._create_pattern_hash(market_conditions, strategy_used, action_taken)
            
            # Determine importance based on outcome
            importance = self._determine_importance(outcome)
            
            # Calculate confidence based on outcome consistency
            confidence = self._calculate_confidence(pattern_hash, outcome)
            
            # Create memory
            memory_id = f"mem_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}_{hash(pattern_hash) % 10000}"
            current_time = datetime.now(timezone.utc)

            # Create comprehensive time context
            time_context = self._create_time_context(current_time)

            # Calculate temporal relevance and decay
            temporal_relevance = self._calculate_temporal_relevance(time_context, outcome)
            time_decay_factor = 1.0  # Initial decay factor

            memory = TradingMemory(
                memory_id=memory_id,
                pattern_type=self._classify_pattern_type(market_conditions, action_taken),
                importance=importance,
                timestamp=current_time,
                time_context=time_context,
                market_conditions=market_conditions,
                strategy_used=strategy_used,
                action_taken=action_taken,
                outcome=outcome,
                context=context or {},
                pattern_hash=pattern_hash,
                confidence=confidence,
                usage_count=0,
                last_accessed=current_time,
                success_rate=1.0 if outcome.get('success', False) else 0.0,
                temporal_relevance=temporal_relevance,
                time_decay_factor=time_decay_factor
            )
            
            # Store memory
            self.memories[memory_id] = memory
            self.recent_memories.append(memory_id)
            
            # Update pattern index
            self.pattern_index[pattern_hash].append(memory_id)
            
            # Save to database
            await self._save_memory_to_database(memory)
            
            # Update pattern performance
            self._update_pattern_performance(pattern_hash, outcome.get('success', False))
            
            self.logger.info(f"💾 Stored trading experience: {memory_id} ({importance.value})")
            
            return memory_id
            
        except Exception as e:
            self.logger.error(f"Error storing trading experience: {e}")
            return ""
    
    async def retrieve_similar_patterns(self, 
                                      current_conditions: Dict[str, Any],
                                      strategy: str,
                                      min_similarity: Optional[float] = None) -> List[PatternMatch]:
        """Retrieve similar patterns from memory"""
        try:
            if min_similarity is None:
                min_similarity = self.similarity_threshold
            
            matches = []
            current_hash = self._create_pattern_hash(current_conditions, strategy, "")
            
            # Search through memories
            for memory_id, memory in self.memories.items():
                similarity = self._calculate_similarity(current_conditions, memory.market_conditions)
                
                if similarity >= min_similarity:
                    # Calculate confidence based on historical success
                    confidence = self._calculate_match_confidence(memory, similarity)
                    
                    # Determine recommended action
                    recommended_action = self._get_recommended_action(memory, current_conditions)
                    
                    # Assess risk
                    risk_assessment = self._assess_pattern_risk(memory, current_conditions)
                    
                    match = PatternMatch(
                        memory=memory,
                        similarity_score=similarity,
                        confidence=confidence,
                        recommended_action=recommended_action,
                        risk_assessment=risk_assessment
                    )
                    
                    matches.append(match)
                    
                    # Update memory usage
                    memory.usage_count += 1
                    memory.last_accessed = datetime.now(timezone.utc)
            
            # Sort by confidence and similarity
            matches.sort(key=lambda x: (x.confidence, x.similarity_score), reverse=True)
            
            self.logger.info(f"🔍 Found {len(matches)} similar patterns (min similarity: {min_similarity:.2f})")
            
            return matches[:10]  # Return top 10 matches
            
        except Exception as e:
            self.logger.error(f"Error retrieving similar patterns: {e}")
            return []
    
    async def update_strategy_memory(self, strategy_name: str, parameters: Dict[str, Any], performance: Dict[str, Any]):
        """Update strategy-specific memory"""
        try:
            if strategy_name not in self.strategy_memory:
                self.strategy_memory[strategy_name] = {
                    'best_parameters': parameters.copy(),
                    'best_performance': performance.copy(),
                    'parameter_history': [],
                    'performance_history': []
                }
            
            strategy_mem = self.strategy_memory[strategy_name]
            
            # Add to history
            strategy_mem['parameter_history'].append({
                'timestamp': datetime.now(timezone.utc),
                'parameters': parameters.copy(),
                'performance': performance.copy()
            })
            
            # Keep only recent history
            if len(strategy_mem['parameter_history']) > 100:
                strategy_mem['parameter_history'] = strategy_mem['parameter_history'][-100:]
            
            # Update best if this performance is better
            current_score = performance.get('profit_factor', 0) * performance.get('win_rate', 0)
            best_score = strategy_mem['best_performance'].get('profit_factor', 0) * strategy_mem['best_performance'].get('win_rate', 0)
            
            if current_score > best_score:
                strategy_mem['best_parameters'] = parameters.copy()
                strategy_mem['best_performance'] = performance.copy()
                
                self.logger.info(f"📈 Updated best parameters for {strategy_name} (score: {current_score:.3f})")
            
            # Save to database
            await self._save_strategy_memory(strategy_name, strategy_mem)
            
        except Exception as e:
            self.logger.error(f"Error updating strategy memory: {e}")
    
    async def learn_from_outcome(self, decision_context: Dict[str, Any], outcome: Dict[str, Any]):
        """Learn from trading outcomes and update patterns"""
        try:
            # Create experience record
            experience_id = await self.store_trading_experience(
                market_conditions=decision_context.get('market_conditions', {}),
                strategy_used=decision_context.get('strategy', ''),
                action_taken=decision_context.get('action', ''),
                outcome=outcome,
                context=decision_context
            )
            
            # Analyze patterns for improvement
            await self._analyze_and_improve_patterns(decision_context, outcome)
            
            self.logger.info(f"🧠 Learned from outcome: {experience_id}")
            
        except Exception as e:
            self.logger.error(f"Error learning from outcome: {e}")
    
    async def get_recommendations(self, current_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get AI recommendations based on current context and past experiences"""
        try:
            # Get similar patterns
            similar_patterns = await self.retrieve_similar_patterns(
                current_context.get('market_conditions', {}),
                current_context.get('strategy', ''),
                min_similarity=self.similarity_threshold
            )
            
            recommendations = []
            for pattern in similar_patterns:
                recommendation = {
                    'action': pattern.recommended_action,
                    'confidence': pattern.confidence,
                    'similarity': pattern.similarity_score,
                    'risk_level': pattern.risk_assessment.get('risk_level', 'medium'),
                    'expected_outcome': pattern.memory.outcome,
                    'reasoning': f"Based on {pattern.memory.usage_count} similar past experiences",
                    'memory_id': pattern.memory.memory_id
                }
                recommendations.append(recommendation)
            
            # Sort by confidence and relevance
            recommendations.sort(key=lambda x: (x['confidence'], x['similarity']), reverse=True)
            
            return recommendations[:5]  # Top 5 recommendations
            
        except Exception as e:
            self.logger.error(f"Error getting recommendations: {e}")
            return []
    
    async def adapt_strategy_parameters(self, strategy_name: str, current_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt strategy parameters based on learned experiences"""
        try:
            if strategy_name not in self.strategy_memory:
                return {}
            
            strategy_mem = self.strategy_memory[strategy_name]
            best_params = strategy_mem['best_parameters'].copy()
            
            # Analyze recent performance trends
            recent_history = strategy_mem['parameter_history'][-10:]  # Last 10 records
            
            if len(recent_history) < 3:
                return best_params
            
            # Calculate performance trend
            recent_scores = []
            for record in recent_history:
                perf = record['performance']
                score = perf.get('profit_factor', 0) * perf.get('win_rate', 0)
                recent_scores.append(score)
            
            # If performance is declining, suggest parameter adjustments
            if len(recent_scores) >= 3:
                trend = np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]
                
                if trend < -0.01:  # Declining performance
                    # Suggest more conservative parameters
                    adjusted_params = best_params.copy()
                    for key, value in adjusted_params.items():
                        if 'risk' in key.lower():
                            adjusted_params[key] = value * 0.8  # Reduce risk
                        elif 'size' in key.lower():
                            adjusted_params[key] = value * 0.9  # Smaller positions
                    
                    self.logger.info(f"📉 Suggesting conservative parameters for {strategy_name}")
                    return adjusted_params
            
            return best_params
            
        except Exception as e:
            self.logger.error(f"Error adapting strategy parameters: {e}")
            return {}
    
    # Private helper methods
    async def _save_memory_to_database(self, memory: TradingMemory):
        """Save memory to database"""
        try:
            # Safety check: ensure self.db is a proper DatabaseManager
            if not self.db or not hasattr(self.db, 'execute'):
                self.logger.error(f"ERROR: Database object missing execute method. Type: {type(self.db)}")
                return

            await self.db.execute(
                """
                INSERT INTO trading_memories 
                (memory_id, pattern_type, importance, timestamp, market_conditions, 
                 strategy_used, action_taken, outcome, context, pattern_hash, 
                 confidence, usage_count, success_rate)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                """,
                memory.memory_id, memory.pattern_type.value, memory.importance.value,
                memory.timestamp, json.dumps(memory.market_conditions),
                memory.strategy_used, memory.action_taken, json.dumps(memory.outcome),
                json.dumps(memory.context), memory.pattern_hash, memory.confidence,
                memory.usage_count, memory.success_rate
            )
        except Exception as e:
            self.logger.error(f"Error saving memory to database: {e}")
    
    async def _save_strategy_memory(self, strategy_name: str, strategy_mem: Dict[str, Any]):
        """Save strategy memory to database"""
        try:
            # Safety check: ensure self.db is a proper DatabaseManager
            if not self.db or not hasattr(self.db, 'execute'):
                self.logger.error(f"ERROR: Database object missing execute method. Type: {type(self.db)}")
                return

            await self.db.execute(
                """
                INSERT INTO strategy_memories (strategy_name, memory_data, updated_at)
                VALUES ($1, $2, $3)
                ON CONFLICT (strategy_name) 
                DO UPDATE SET memory_data = $2, updated_at = $3
                """,
                strategy_name, json.dumps(strategy_mem), datetime.now(timezone.utc)
            )
        except Exception as e:
            self.logger.error(f"Error saving strategy memory: {e}")
    
    async def _load_memories_from_database(self):
        """Load existing memories from database"""
        try:
            # Check if database manager is available
            if not self.db:
                self.logger.warning("Database manager not available - skipping memory loading")
                return

            # Safety check: ensure self.db is a proper DatabaseManager
            if not hasattr(self.db, 'fetch_all'):
                self.logger.error(f"ERROR: Database object missing fetch_all method. Type: {type(self.db)}")
                self.logger.error("This indicates a circular reference or incorrect initialization")
                return

            # Check if table exists and has data
            if self.db is None:
                self.logger.error("ERROR: Database manager is None - cannot load memories")
                return
            if not hasattr(self.db, 'fetch_all'):
                self.logger.error(f"ERROR: Database object missing fetch_all method. Type: {type(self.db)}")
                return

            rows = await self.db.fetch_all(
                "SELECT * FROM trading_memories ORDER BY timestamp DESC LIMIT 1000"
            )

            if not rows:
                self.logger.info("No existing memories found in database - starting fresh")
                return

            for row in rows:
                try:
                    # Map database columns to TradingMemory fields
                    # Database schema: id, symbol, timestamp, pattern_data, pattern_hash, outcome,
                    # success, profit_loss, strategy, confidence, market_conditions, technical_indicators, metadata

                    memory_id = str(row.get('id', f"mem_{len(self.memories)}"))
                    pattern_data = json.loads(row.get('pattern_data', '{}')) if row.get('pattern_data') else {}
                    market_conditions = json.loads(row.get('market_conditions', '{}')) if row.get('market_conditions') else {}
                    outcome_data = json.loads(row.get('outcome', '{}')) if row.get('outcome') else {}
                    metadata = json.loads(row.get('metadata', '{}')) if row.get('metadata') else {}

                    # Create TradingMemory with available data
                    memory = TradingMemory(
                        memory_id=memory_id,
                        pattern_type=PatternType.PRICE_PATTERN,  # Default pattern type
                        importance=MemoryImportance.MEDIUM,  # Default importance
                        timestamp=row.get('timestamp', datetime.now(timezone.utc)),
                        market_conditions=market_conditions,
                        strategy_used=row.get('strategy', 'unknown'),
                        action_taken=outcome_data.get('action', 'hold'),
                        outcome=outcome_data,
                        context=metadata,
                        pattern_hash=row.get('pattern_hash', ''),
                        confidence=float(row.get('confidence', 0.5)),
                        usage_count=1,
                        last_accessed=row.get('timestamp', datetime.now(timezone.utc)),
                        success_rate=1.0 if row.get('success', False) else 0.0
                    )
                    self.memories[memory.memory_id] = memory

                except Exception as row_error:
                    self.logger.warning(f"Error processing memory row: {row_error}")
                    continue

            self.logger.info(f"Loaded {len(self.memories)} memories from database")

        except Exception as e:
            self.logger.error(f"Error loading memories from database: {e}")
            # Don't raise - allow system to continue with empty memories
    
    async def _build_pattern_index(self):
        """Build pattern index for fast similarity search"""
        try:
            for memory_id, memory in self.memories.items():
                self.pattern_index[memory.pattern_hash].append(memory_id)
        except Exception as e:
            self.logger.error(f"Error building pattern index: {e}")
    
    async def _load_strategy_memories(self):
        """Load strategy memories from database"""
        try:
            # Check if database manager is available
            if not self.db:
                self.logger.warning("Database manager not available - skipping strategy memory loading")
                return

            # Safety check: ensure self.db is a proper DatabaseManager
            if self.db is None:
                self.logger.error("ERROR: Database manager is None - cannot load strategy memories")
                return
            if not hasattr(self.db, 'fetch_all'):
                self.logger.error(f"ERROR: Database object missing fetch_all method. Type: {type(self.db)}")
                self.logger.error("This indicates a circular reference or incorrect initialization")
                return

            rows = await self.db.fetch_all("SELECT * FROM strategy_memories")
            
            for row in rows:
                strategy_name = row['strategy_name']
                memory_data = json.loads(row['memory_data'])
                self.strategy_memory[strategy_name] = memory_data
                
        except Exception as e:
            self.logger.error(f"Error loading strategy memories: {e}")

    def _create_time_context(self, timestamp: datetime) -> TimeContext:
        """Create comprehensive time context"""
        try:
            # Convert to UTC if not already
            if timestamp.tzinfo is None:
                timestamp = timestamp.replace(tzinfo=ZoneInfo('UTC'))

            # Extract time components
            year = timestamp.year
            month = timestamp.month
            day = timestamp.day
            hour = timestamp.hour
            minute = timestamp.minute
            second = timestamp.second
            microsecond = timestamp.microsecond

            # Calculate derived time values
            day_of_week = timestamp.weekday()  # 0=Monday, 6=Sunday
            day_of_year = timestamp.timetuple().tm_yday
            week_of_year = timestamp.isocalendar()[1]
            quarter = (month - 1) // 3 + 1

            # Weekend and holiday detection
            is_weekend = day_of_week >= 5  # Saturday=5, Sunday=6
            is_holiday = False
            holiday_name = None

            # Check holidays in major markets
            date_only = timestamp.date()
            for country, holiday_calendar in self.holidays.items():
                if date_only in holiday_calendar:
                    is_holiday = True
                    holiday_name = holiday_calendar.get(date_only, f"{country} Holiday")
                    break

            # Market session detection
            market_session = self._determine_market_session(hour)

            # Time of day classification
            time_of_day = self._classify_time_of_day(hour)

            # Timezone info
            timezone_str = str(timestamp.tzinfo)
            utc_offset_td = timestamp.utcoffset()
            utc_offset = int(utc_offset_td.total_seconds() / 3600) if utc_offset_td is not None else 0

            # Market timing (simplified - would need actual market hours)
            time_since_market_open = None
            time_until_market_close = None

            return TimeContext(
                timestamp=timestamp,
                year=year,
                month=month,
                day=day,
                hour=hour,
                minute=minute,
                second=second,
                microsecond=microsecond,
                day_of_week=day_of_week,
                day_of_year=day_of_year,
                week_of_year=week_of_year,
                quarter=quarter,
                is_weekend=is_weekend,
                is_holiday=is_holiday,
                holiday_name=holiday_name,
                market_session=market_session,
                time_of_day=time_of_day,
                timezone=timezone_str,
                utc_offset=utc_offset,
                time_since_market_open=time_since_market_open,
                time_until_market_close=time_until_market_close
            )

        except Exception as e:
            self.logger.error(f"Error creating time context: {e}")
            # Return minimal time context
            return TimeContext(
                timestamp=timestamp,
                year=timestamp.year,
                month=timestamp.month,
                day=timestamp.day,
                hour=timestamp.hour,
                minute=timestamp.minute,
                second=timestamp.second,
                microsecond=timestamp.microsecond,
                day_of_week=timestamp.weekday(),
                day_of_year=timestamp.timetuple().tm_yday,
                week_of_year=timestamp.isocalendar()[1],
                quarter=(timestamp.month - 1) // 3 + 1,
                is_weekend=timestamp.weekday() >= 5,
                is_holiday=False,
                holiday_name=None,
                market_session="UNKNOWN",
                time_of_day="UNKNOWN",
                timezone="UTC",
                utc_offset=0,
                time_since_market_open=None,
                time_until_market_close=None
            )

    def _determine_market_session(self, hour: int) -> str:
        """Determine market session based on UTC hour"""
        if 0 <= hour < 9:
            return "ASIAN"
        elif 7 <= hour < 16:
            if 7 <= hour < 9:
                return "ASIAN_EUROPEAN_OVERLAP"
            else:
                return "EUROPEAN"
        elif 13 <= hour < 22:
            if 13 <= hour < 16:
                return "EUROPEAN_US_OVERLAP"
            else:
                return "US"
        else:
            return "AFTER_HOURS"

    def _classify_time_of_day(self, hour: int) -> str:
        """Classify time of day"""
        if 6 <= hour < 12:
            return "MORNING"
        elif 12 <= hour < 18:
            return "AFTERNOON"
        elif 18 <= hour < 24:
            return "EVENING"
        else:
            return "NIGHT"

    def _calculate_temporal_relevance(self, time_context: TimeContext, outcome: Dict[str, Any]) -> float:
        """Calculate temporal relevance score"""
        relevance = 0.5  # Base relevance

        # Boost for successful outcomes
        if outcome.get('success', False):
            relevance += 0.2

        # Boost for profitable outcomes
        profit = outcome.get('profit', 0)
        if profit > 0:
            relevance += min(0.2, profit / 1000)

        # Boost for active market sessions
        if time_context.market_session in ['EUROPEAN', 'US', 'EUROPEAN_US_OVERLAP']:
            relevance += 0.1

        # Reduce for holidays and weekends
        if time_context.is_holiday:
            relevance -= 0.1
        if time_context.is_weekend:
            relevance -= 0.05

        # Boost for optimal trading hours
        if time_context.time_of_day in ['MORNING', 'AFTERNOON']:
            relevance += 0.05

        return max(0.0, min(1.0, relevance))

    async def save_strategy_state(self, strategy_name: str, positions: Dict[str, Any],
                                parameters: Dict[str, Any], performance: Dict[str, float],
                                continuation_data: Optional[Dict[str, Any]] = None) -> str:
        """Save strategy state for continuation after system restart"""
        try:
            current_time = datetime.now(timezone.utc)

            # Create state hash for integrity checking
            state_data = {
                'positions': positions,
                'parameters': parameters,
                'performance': performance,
                'continuation_data': continuation_data or {}
            }
            state_hash = hashlib.sha256(json.dumps(state_data, sort_keys=True).encode()).hexdigest()

            # Calculate time since last run if exists
            time_since_last_run = timedelta(0)
            if strategy_name in self.strategy_states:
                time_since_last_run = current_time - self.strategy_states[strategy_name].last_run_timestamp

            # Create strategy state
            strategy_state = StrategyState(
                strategy_name=strategy_name,
                last_run_timestamp=current_time,
                time_since_last_run=time_since_last_run,
                last_positions=positions.copy(),
                last_parameters=parameters.copy(),
                last_performance=performance.copy(),
                continuation_data=continuation_data or {},
                state_hash=state_hash
            )

            # Store in memory
            self.strategy_states[strategy_name] = strategy_state

            # Save to database
            await self._save_strategy_state_to_db(strategy_state)

            self.logger.info(f"Strategy state saved for {strategy_name}")
            return state_hash

        except Exception as e:
            self.logger.error(f"Error saving strategy state: {e}")
            raise

    async def load_strategy_state(self, strategy_name: str) -> Optional[StrategyState]:
        """Load strategy state for continuation"""
        try:
            # First check memory
            if strategy_name in self.strategy_states:
                state = self.strategy_states[strategy_name]
                # Update time since last run
                current_time = datetime.now(timezone.utc)
                state.time_since_last_run = current_time - state.last_run_timestamp
                return state

            # Load from database
            loaded_state = await self._load_strategy_state_from_db(strategy_name)
            if loaded_state:
                # Update time since last run
                current_time = datetime.now(timezone.utc)
                loaded_state.time_since_last_run = current_time - loaded_state.last_run_timestamp
                self.strategy_states[strategy_name] = loaded_state

                self.logger.info(f"Strategy state loaded for {strategy_name}. "
                               f"Time since last run: {state.time_since_last_run}")
                return state

            return None

        except Exception as e:
            self.logger.error(f"Error loading strategy state: {e}")
            return None

    async def get_system_downtime(self) -> Optional[timedelta]:
        """Get total system downtime since last run"""
        try:
            if self.last_system_run is None:
                # Load from database
                if not self.db:
                    self.logger.warning("Database manager not available - cannot get system downtime")
                    return None

                # Safety check: ensure self.db is a proper DatabaseManager
                if not hasattr(self.db, 'fetch_one'):
                    self.logger.error(f"ERROR: Database object missing fetch_one method. Type: {type(self.db)}")
                    return None

                row = await self.db.fetch_one(
                    "SELECT last_run_timestamp FROM system_state WHERE component = 'memory_manager'"
                )
                if row:
                    self.last_system_run = row['last_run_timestamp']

            if self.last_system_run:
                current_time = datetime.now(timezone.utc)
                self.system_downtime = current_time - self.last_system_run
                return self.system_downtime

            return None

        except Exception as e:
            self.logger.error(f"Error calculating system downtime: {e}")
            return None

    async def update_system_run_time(self):
        """Update system last run time"""
        try:
            current_time = datetime.now(timezone.utc)
            self.last_system_run = current_time

            # Save to database
            if not self.db:
                self.logger.warning("Database manager not available - cannot update system run time")
                return

            # Safety check: ensure self.db is a proper DatabaseManager
            if not hasattr(self.db, 'execute_sql'):
                self.logger.error(f"ERROR: Database object missing execute_sql method. Type: {type(self.db)}")
                return

            await self.db.execute_sql(
                """
                INSERT INTO system_state (component, last_run_timestamp, state_data)
                VALUES (:component, :timestamp, :state_data)
                ON CONFLICT (component)
                DO UPDATE SET last_run_timestamp = :timestamp, updated_at = CURRENT_TIMESTAMP
                """,
                {
                    'component': 'memory_manager',
                    'timestamp': current_time,
                    'state_data': '{}'
                }
            )

        except Exception as e:
            self.logger.error(f"Error updating system run time: {e}")

    async def _save_strategy_state_to_db(self, state: StrategyState):
        """Save strategy state to database"""
        try:
            state_data = {
                'last_run_timestamp': state.last_run_timestamp.isoformat(),
                'time_since_last_run': state.time_since_last_run.total_seconds(),
                'last_positions': state.last_positions,
                'last_parameters': state.last_parameters,
                'last_performance': state.last_performance,
                'continuation_data': state.continuation_data,
                'state_hash': state.state_hash
            }

            # Safety check: ensure self.db is a proper DatabaseManager
            if not self.db or not hasattr(self.db, 'execute'):
                self.logger.error(f"ERROR: Database object missing execute method. Type: {type(self.db)}")
                return

            await self.db.execute(
                """
                INSERT INTO strategy_states (strategy_name, state_data, state_hash, updated_at)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (strategy_name)
                DO UPDATE SET state_data = $2, state_hash = $3, updated_at = $4
                """,
                state.strategy_name, json.dumps(state_data), state.state_hash, state.last_run_timestamp
            )

        except Exception as e:
            self.logger.error(f"Error saving strategy state to database: {e}")

    async def _load_strategy_state_from_db(self, strategy_name: str) -> Optional[StrategyState]:
        """Load strategy state from database"""
        try:
            if not self.db:
                self.logger.warning("Database manager not available - cannot load strategy state")
                return None

            # Safety check: ensure self.db is a proper DatabaseManager
            if not hasattr(self.db, 'fetch_one'):
                self.logger.error(f"ERROR: Database object missing fetch_one method. Type: {type(self.db)}")
                return None

            row = await self.db.fetch_one(
                "SELECT * FROM strategy_states WHERE strategy_name = $1",
                strategy_name
            )

            if row:
                state_data = json.loads(row['state_data'])

                return StrategyState(
                    strategy_name=strategy_name,
                    last_run_timestamp=datetime.fromisoformat(state_data['last_run_timestamp']),
                    time_since_last_run=timedelta(seconds=state_data['time_since_last_run']),
                    last_positions=state_data['last_positions'],
                    last_parameters=state_data['last_parameters'],
                    last_performance=state_data['last_performance'],
                    continuation_data=state_data['continuation_data'],
                    state_hash=state_data['state_hash']
                )

            return None

        except Exception as e:
            self.logger.error(f"Error loading strategy state from database: {e}")
            return None

    async def get_time_aware_memories_by_context(self, time_context: TimeContext,
                                               similarity_threshold: float = 0.8) -> List[TradingMemory]:
        """Get memories that match specific time context"""
        try:
            matching_memories = []

            for memory in self.memories.values():
                if hasattr(memory, 'time_context'):
                    similarity = self._calculate_time_context_similarity(time_context, memory.time_context)
                    if similarity >= similarity_threshold:
                        matching_memories.append(memory)

            # Sort by temporal relevance and recency
            matching_memories.sort(
                key=lambda m: (m.temporal_relevance * m.time_decay_factor, m.timestamp),
                reverse=True
            )

            return matching_memories

        except Exception as e:
            self.logger.error(f"Error getting time-aware memories: {e}")
            return []

    def _calculate_time_context_similarity(self, context1: TimeContext, context2: TimeContext) -> float:
        """Calculate similarity between time contexts"""
        try:
            similarity = 0.0
            total_weight = 0.0

            # Hour similarity (circular)
            hour_diff = min(abs(context1.hour - context2.hour), 24 - abs(context1.hour - context2.hour))
            hour_similarity = 1.0 - (hour_diff / 12.0)
            similarity += 0.3 * hour_similarity
            total_weight += 0.3

            # Day of week similarity (circular)
            dow_diff = min(abs(context1.day_of_week - context2.day_of_week),
                          7 - abs(context1.day_of_week - context2.day_of_week))
            dow_similarity = 1.0 - (dow_diff / 3.5)
            similarity += 0.2 * dow_similarity
            total_weight += 0.2

            # Market session similarity
            if context1.market_session == context2.market_session:
                similarity += 0.25
            total_weight += 0.25

            # Time of day similarity
            if context1.time_of_day == context2.time_of_day:
                similarity += 0.15
            total_weight += 0.15

            # Holiday/weekend similarity
            if context1.is_holiday == context2.is_holiday:
                similarity += 0.05
            if context1.is_weekend == context2.is_weekend:
                similarity += 0.05
            total_weight += 0.1

            return similarity / total_weight if total_weight > 0 else 0.0

        except Exception as e:
            self.logger.error(f"Error calculating time context similarity: {e}")
            return 0.0
    
    def _create_pattern_hash(self, market_conditions: Dict[str, Any], strategy: str, action: str) -> str:
        """Create a hash for pattern matching"""
        try:
            # Extract key features for hashing
            features = {
                'volatility': market_conditions.get('volatility', 0),
                'trend': market_conditions.get('trend_direction', 'neutral'),
                'volume': market_conditions.get('volume_normalized', 0),
                'rsi': market_conditions.get('rsi', 50),
                'strategy': strategy,
                'action_type': action.split('_')[0] if '_' in action else action
            }
            
            # Create hash
            feature_string = json.dumps(features, sort_keys=True)
            return hashlib.md5(feature_string.encode()).hexdigest()
            
        except Exception as e:
            self.logger.error(f"Error creating pattern hash: {e}")
            return ""
    
    def _determine_importance(self, outcome: Dict[str, Any]) -> MemoryImportance:
        """Determine importance of a trading outcome"""
        try:
            pnl = outcome.get('pnl', 0)
            success = outcome.get('success', False)
            
            if abs(pnl) > 1000:  # Large PnL
                return MemoryImportance.CRITICAL
            elif abs(pnl) > 500:
                return MemoryImportance.HIGH
            elif success:
                return MemoryImportance.MEDIUM
            else:
                return MemoryImportance.LOW
                
        except Exception:
            return MemoryImportance.LOW
    
    def _calculate_confidence(self, pattern_hash: str, outcome: Dict[str, Any]) -> float:
        """Calculate confidence based on historical pattern performance"""
        try:
            pattern_perf = self.pattern_performance[pattern_hash]
            if pattern_perf['total'] == 0:
                return 0.5  # Default confidence for new patterns
            
            success_rate = pattern_perf['success'] / pattern_perf['total']
            return min(0.95, max(0.05, success_rate))
            
        except Exception:
            return 0.5
    
    def _classify_pattern_type(self, market_conditions: Dict[str, Any], action: str) -> PatternType:
        """Classify the type of pattern"""
        try:
            if 'volatility' in market_conditions and market_conditions['volatility'] > 0.5:
                return PatternType.MARKET_CONDITION
            elif 'volume' in market_conditions:
                return PatternType.VOLUME_PATTERN
            elif 'trend' in str(action).lower():
                return PatternType.PRICE_PATTERN
            else:
                return PatternType.STRATEGY_PERFORMANCE
                
        except Exception:
            return PatternType.STRATEGY_PERFORMANCE
    
    def _calculate_similarity(self, conditions1: Dict[str, Any], conditions2: Dict[str, Any]) -> float:
        """Calculate similarity between market conditions"""
        try:
            if not conditions1 or not conditions2:
                return 0.0
            
            # Extract common features
            features1 = {}
            features2 = {}
            
            for key in self.feature_weights:
                features1[key] = conditions1.get(key, 0)
                features2[key] = conditions2.get(key, 0)
            
            # Calculate weighted similarity
            total_similarity = 0.0
            total_weight = 0.0
            
            for key, weight in self.feature_weights.items():
                val1 = features1.get(key, 0)
                val2 = features2.get(key, 0)
                
                # Normalize and calculate similarity
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    max_val = max(abs(val1), abs(val2), 1)
                    similarity = 1.0 - abs(val1 - val2) / max_val
                    total_similarity += similarity * weight
                    total_weight += weight
                elif val1 == val2:
                    total_similarity += weight
                    total_weight += weight
            
            return total_similarity / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def _calculate_match_confidence(self, memory: TradingMemory, similarity: float) -> float:
        """Calculate confidence for a pattern match"""
        try:
            base_confidence = memory.confidence
            usage_bonus = min(0.2, memory.usage_count * 0.01)
            similarity_factor = similarity
            
            return min(0.95, base_confidence * similarity_factor + usage_bonus)
            
        except Exception:
            return 0.5
    
    def _get_recommended_action(self, memory: TradingMemory, current_conditions: Dict[str, Any]) -> str:
        """Get recommended action based on memory"""
        try:
            if memory.outcome.get('success', False):
                return memory.action_taken
            else:
                # Suggest opposite or modified action
                action = memory.action_taken
                if 'buy' in action.lower():
                    return action.replace('buy', 'sell')
                elif 'sell' in action.lower():
                    return action.replace('sell', 'buy')
                else:
                    return 'hold'
                    
        except Exception:
            return 'hold'
    
    def _assess_pattern_risk(self, memory: TradingMemory, current_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for a pattern match"""
        try:
            historical_pnl = memory.outcome.get('pnl', 0)
            success_rate = memory.success_rate
            
            if success_rate > 0.8:
                risk_level = 'low'
            elif success_rate > 0.6:
                risk_level = 'medium'
            else:
                risk_level = 'high'
            
            return {
                'risk_level': risk_level,
                'historical_pnl': historical_pnl,
                'success_rate': success_rate,
                'recommendation': 'proceed' if risk_level != 'high' else 'caution'
            }
            
        except Exception:
            return {'risk_level': 'high', 'recommendation': 'caution'}
    
    def _update_pattern_performance(self, pattern_hash: str, success: bool):
        """Update pattern performance statistics"""
        try:
            self.pattern_performance[pattern_hash]['total'] += 1
            if success:
                self.pattern_performance[pattern_hash]['success'] += 1
        except Exception as e:
            self.logger.error(f"Error updating pattern performance: {e}")
    
    async def _analyze_and_improve_patterns(self, decision_context: Dict[str, Any], outcome: Dict[str, Any]):
        """Analyze outcomes and improve pattern recognition"""
        try:
            # This is where advanced AI learning would happen
            # For now, we update basic statistics
            pattern_hash = self._create_pattern_hash(
                decision_context.get('market_conditions', {}),
                decision_context.get('strategy', ''),
                decision_context.get('action', '')
            )
            
            success = outcome.get('success', False)
            self._update_pattern_performance(pattern_hash, success)
            
        except Exception as e:
            self.logger.error(f"Error analyzing patterns: {e}")


# Alias for backward compatibility and simpler imports
MemoryManager = PersistentMemoryManager