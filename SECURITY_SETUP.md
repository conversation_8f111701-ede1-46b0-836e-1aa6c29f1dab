---
type: "manual"
---

# 🛡️ SECURITY AND CONFIGURATION SETUP

## ⚠️ IMPORTANT SECURITY NOTICE

This repository contains templates for configuration files. **NEVER commit actual API keys, passwords, or secrets to version control.**

## 🔧 Setup Instructions

### 1. Environment Configuration

Copy the environment template and configure your credentials:

```bash
cp .env.template .env
```

Then edit `.env` with your actual API keys and credentials.

### 2. Application Configuration

Copy the secure configuration template:

```bash
cp config_secure_template.yaml config.yaml
```

Then edit `config.yaml` with your actual settings.

### 3. Required API Keys

You'll need to obtain API keys from:

- **Bybit**: [API Management](https://www.bybit.com/app/user/api-management)
- **OpenAI**: [API Keys](https://platform.openai.com/api-keys)
- **News API**: [NewsAPI.org](https://newsapi.org/)
- **Alpha Vantage**: [Free API Key](https://www.alphavantage.co/support/#api-key)
- **FRED**: [Economic Data API](https://fred.stlouisfed.org/docs/api/api_key.html)
- **Twitter**: [Developer Portal](https://developer.twitter.com/)
- **Reddit**: [App Preferences](https://www.reddit.com/prefs/apps)

### 4. Database Setup

Set up PostgreSQL:

```bash
# Install PostgreSQL (Windows)
# Download from https://www.postgresql.org/download/windows/

# Create database
createdb bybit_trading_bot

# Update your .env file with the database credentials
```

### 5. Security Best Practices

- ✅ Use environment variables for all secrets
- ✅ Keep `.env` and `config.yaml` files local only
- ✅ Use strong, unique passwords
- ✅ Enable 2FA on all API accounts
- ✅ Regularly rotate API keys
- ✅ Use minimal required permissions for API keys
- ✅ Monitor API usage for suspicious activity

### 6. File Permissions

On Linux/macOS, secure your configuration files:

```bash
chmod 600 .env
chmod 600 config.yaml
```

## 🚨 Files to NEVER Commit

- `.env` (contains real API keys)
- `config.yaml` (contains real configuration)
- `*.key` files
- `secrets/` directory
- Any file containing actual passwords or API keys

## 🔍 Checking for Exposed Secrets

Before committing, always check:

```bash
# Search for potential secrets in tracked files
git grep -n "api_key\|secret\|password\|token" -- '*.py' '*.yaml' '*.yml' '*.md'

# Check git status to ensure no sensitive files are staged
git status
```

## 🔒 Emergency Response

If you accidentally commit secrets:

1. **Immediately revoke/rotate the exposed keys**
2. **Remove from git history**: Use `git filter-branch` or BFG Repo-Cleaner
3. **Force push to overwrite history**: `git push --force`
4. **Notify team members** to re-clone the repository

## 📞 Support

For security issues, contact the development team immediately.
