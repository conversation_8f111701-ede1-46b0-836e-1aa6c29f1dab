#!/usr/bin/env python3
"""
Test database manager methods to verify they exist and work correctly
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_database_methods():
    """Test database manager methods"""
    print("TESTING: Database Manager Methods")
    print("=" * 50)
    
    try:
        # Import the database manager
        from bybit_bot.database.connection import DatabaseManager
        from bybit_bot.core.config import BotConfig
        
        print("SUCCESS: Imports successful")
        
        # Create config and database manager
        config = BotConfig()
        db_manager = DatabaseManager(config)
        
        print("SUCCESS: DatabaseManager instance created")
        
        # Check if methods exist
        methods_to_check = ['fetch_all', 'fetch_one', 'execute_sql', 'execute_query']
        
        for method in methods_to_check:
            if hasattr(db_manager, method):
                print(f"SUCCESS: {method} method exists")
            else:
                print(f"ERROR: {method} method NOT FOUND")
        
        # Try to initialize
        try:
            await db_manager.initialize()
            print("SUCCESS: Database initialized")
            
            # Test a simple query
            try:
                result = await db_manager.execute_sql("SELECT 1 as test")
                print(f"SUCCESS: execute_sql works: {result}")
            except Exception as e:
                print(f"ERROR: execute_sql failed: {e}")
            
            # Test fetch_all
            try:
                result = await db_manager.fetch_all("SELECT 1 as test")
                print(f"SUCCESS: fetch_all works: {result}")
            except Exception as e:
                print(f"ERROR: fetch_all failed: {e}")
            
            # Test fetch_one
            try:
                result = await db_manager.fetch_one("SELECT 1 as test")
                print(f"SUCCESS: fetch_one works: {result}")
            except Exception as e:
                print(f"ERROR: fetch_one failed: {e}")
                
        except Exception as e:
            print(f"ERROR: Database initialization failed: {e}")
        
    except Exception as e:
        print(f"ERROR: Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database_methods())
