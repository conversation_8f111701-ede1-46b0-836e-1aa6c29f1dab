#!/usr/bin/env python3
"""
Debug Environment Test
"""

print("=== ENVIRONMENT DEBUG TEST ===")
print("1. Testing basic Python execution...")

try:
    import sys
    print(f"SUCCESS: Python version: {sys.version}")
    print(f"SUCCESS: Python executable: {sys.executable}")
    print(f"SUCCESS: Python path: {sys.path[:3]}...")
except Exception as e:
    print(f"ERROR: Basic Python test failed: {e}")

print("\n2. Testing imports...")

try:
    import os
    print("SUCCESS: os module imported")
except Exception as e:
    print(f"ERROR: os import failed: {e}")

try:
    import asyncio
    print("SUCCESS: asyncio module imported")
except Exception as e:
    print(f"ERROR: asyncio import failed: {e}")

try:
    import logging
    print("SUCCESS: logging module imported")
except Exception as e:
    print(f"ERROR: logging import failed: {e}")

try:
    from pathlib import Path
    print("SUCCESS: pathlib imported")
except Exception as e:
    print(f"ERROR: pathlib import failed: {e}")

try:
    from dotenv import load_dotenv
    print("SUCCESS: dotenv imported")
except Exception as e:
    print(f"ERROR: dotenv import failed: {e}")

print("\n3. Testing bybit_bot imports...")

try:
    from bybit_bot.core.config import BotConfig
    print("SUCCESS: BotConfig imported")
except Exception as e:
    print(f"ERROR: BotConfig import failed: {e}")

try:
    from bybit_bot.database.connection import DatabaseManager
    print("SUCCESS: DatabaseManager imported")
except Exception as e:
    print(f"ERROR: DatabaseManager import failed: {e}")

try:
    from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
    print("SUCCESS: EnhancedBybitClient imported")
except Exception as e:
    print(f"ERROR: EnhancedBybitClient import failed: {e}")

print("\n4. Testing configuration...")

try:
    config = BotConfig()
    print("SUCCESS: BotConfig created")
    print(f"SUCCESS: Config has {len(config.__dict__)} attributes")
except Exception as e:
    print(f"ERROR: BotConfig creation failed: {e}")

print("\n5. Testing async functionality...")

async def test_async():
    print("SUCCESS: Async function executed")
    return True

try:
    import asyncio
    result = asyncio.run(test_async())
    print(f"SUCCESS: Asyncio.run completed with result: {result}")
except Exception as e:
    print(f"ERROR: Asyncio test failed: {e}")

print("\n=== ENVIRONMENT DEBUG COMPLETE ===")
print("If all tests passed, the environment is ready for trading bot execution")
