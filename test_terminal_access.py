"""
Terminal Access Test Script
This script helps verify that <PERSON><PERSON><PERSON> can access the terminal properly
"""

import subprocess
import sys
import os
from pathlib import Path

def test_terminal_access():
    """Test basic terminal functionality"""
    print("[INFO] Testing terminal access for Copilot...")
    
    # Test 1: Basic command execution
    try:
        result = subprocess.run(['python', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"[OK] Python version: {result.stdout.strip()}")
        else:
            print(f"[ERROR] Python test failed: {result.stderr}")
    except Exception as e:
        print(f"[ERROR] Python test exception: {e}")
    
    # Test 2: Environment variables
    print(f"[INFO] PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
    print(f"[INFO] CONDA_DEFAULT_ENV: {os.environ.get('CONDA_DEFAULT_ENV', 'Not set')}")
    print(f"[INFO] Current directory: {os.getcwd()}")
    
    # Test 3: Conda environment
    try:
        result = subprocess.run(['conda', 'info', '--envs'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("[OK] Conda environments accessible")
        else:
            print(f"[WARNING] Conda access issue: {result.stderr}")
    except Exception as e:
        print(f"[WARNING] Conda test exception: {e}")
    
    print("[INFO] Terminal access test completed")
    return True

if __name__ == "__main__":
    test_terminal_access()
