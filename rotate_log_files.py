#!/usr/bin/env python3
"""
Rotate Log Files - Manage oversized log files and implement 5GB limit
"""

import os
import shutil
from datetime import datetime

def rotate_log_files():
    """Rotate oversized log files and implement 5GB limit"""
    
    print("=== LOG FILE ROTATION ===")
    print(f"Time: {datetime.now()}")
    
    log_files_to_check = [
        "logs/bybit_trading_bot.log",
        "logs/system_startup.log",
        "logs/streamlined_system.log"
    ]
    
    max_size_gb = 5
    max_size_bytes = max_size_gb * 1024 * 1024 * 1024  # 5GB in bytes
    
    for log_file in log_files_to_check:
        if os.path.exists(log_file):
            try:
                file_size = os.path.getsize(log_file)
                file_size_gb = file_size / (1024 * 1024 * 1024)
                
                print(f"\nChecking {log_file}:")
                print(f"  Current size: {file_size_gb:.2f} GB ({file_size:,} bytes)")
                
                if file_size > max_size_bytes:
                    print(f"  WARNING: File exceeds {max_size_gb}GB limit!")
                    
                    # Create backup filename with timestamp
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_file = f"{log_file}.backup_{timestamp}"
                    
                    print(f"  Creating backup: {backup_file}")
                    shutil.move(log_file, backup_file)
                    
                    # Create new empty log file
                    print(f"  Creating new empty log file: {log_file}")
                    with open(log_file, 'w') as f:
                        f.write(f"# Log file rotated at {datetime.now()}\n")
                        f.write(f"# Previous log backed up to: {backup_file}\n")
                        f.write(f"# Maximum log size: {max_size_gb}GB\n\n")
                    
                    print(f"  SUCCESS: Log file rotated")
                    
                    # Check if we have too many backup files (keep only 3 most recent)
                    log_dir = os.path.dirname(log_file)
                    log_name = os.path.basename(log_file)
                    backup_files = []
                    
                    for file in os.listdir(log_dir):
                        if file.startswith(f"{log_name}.backup_"):
                            backup_path = os.path.join(log_dir, file)
                            backup_files.append((backup_path, os.path.getmtime(backup_path)))
                    
                    # Sort by modification time (newest first)
                    backup_files.sort(key=lambda x: x[1], reverse=True)
                    
                    # Remove old backups (keep only 3 most recent)
                    if len(backup_files) > 3:
                        for old_backup, _ in backup_files[3:]:
                            print(f"  Removing old backup: {old_backup}")
                            os.remove(old_backup)
                
                else:
                    print(f"  OK: File size within {max_size_gb}GB limit")
                    
            except Exception as e:
                print(f"  ERROR: Failed to process {log_file}: {e}")
        else:
            print(f"\nSkipping {log_file}: File does not exist")
    
    print("\n=== LOG ROTATION COMPLETE ===")

if __name__ == "__main__":
    rotate_log_files()
