#!/usr/bin/env python3
"""
Test if the system is working properly
"""
import sys
import subprocess
import time

def test_six():
    """Test if six is available"""
    try:
        import six
        print(f"SUCCESS: six is available (version {six.__version__})")
        return True
    except ImportError:
        print("ERROR: six not available")
        return False

def install_six():
    """Install six package"""
    try:
        print("Installing six...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "six"], 
                              check=True, capture_output=True, text=True, timeout=30)
        print("Installation successful!")
        return True
    except Exception as e:
        print(f"Installation failed: {e}")
        return False

def main():
    print("BYBIT TRADING BOT - SYSTEM TEST")
    print("=" * 40)
    
    # Test six availability
    if not test_six():
        print("Attempting to install six...")
        if install_six():
            if test_six():
                print("Six installation successful!")
            else:
                print("Six installation failed!")
                return False
        else:
            print("Could not install six")
            return False
    
    print("All dependencies ready!")
    print("System test completed successfully!")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\nSix dependency is working!")
        print("The main system should now run without the six error.")
    else:
        print("\nSix dependency issue not resolved.")
