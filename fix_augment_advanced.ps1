# Augment Extension Crash Diagnostics and Fix
# Advanced PowerShell script to identify and fix extension host crashes

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "AUGMENT EXTENSION CRASH DIAGNOSTICS" -ForegroundColor Cyan  
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Function to check Windows Event Logs for extension crashes
function Get-ExtensionCrashes {
    Write-Host "[DIAGNOSTIC] Checking Windows Event Logs for VS Code crashes..." -ForegroundColor Yellow
    try {
        $crashes = Get-WinEvent -FilterHashtable @{LogName='Application'; ProviderName='Windows Error Reporting'} -MaxEvents 10 -ErrorAction SilentlyContinue | 
                   Where-Object { $_.Message -like "*Code*" -or $_.Message -like "*electron*" }
        
        if ($crashes) {
            Write-Host "[FOUND] Recent VS Code crashes detected:" -ForegroundColor Red
            $crashes | ForEach-Object { 
                Write-Host "  Time: $($_.TimeCreated) - $($_.LevelDisplayName)" -ForegroundColor Red
            }
        } else {
            Write-Host "[OK] No recent VS Code crashes found in Event Log" -ForegroundColor Green
        }
    } catch {
        Write-Host "[WARNING] Could not access Event Log: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    Write-Host ""
}

# Function to check memory usage
function Test-SystemResources {
    Write-Host "[DIAGNOSTIC] Checking system resources..." -ForegroundColor Yellow
    
    $memory = Get-CimInstance -ClassName Win32_OperatingSystem
    $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    $memoryUsagePercent = [math]::Round((($totalMemoryGB - $freeMemoryGB) / $totalMemoryGB) * 100, 1)
    
    Write-Host "  Memory Usage: $memoryUsagePercent% ($freeMemoryGB GB free of $totalMemoryGB GB)" -ForegroundColor Cyan
    
    if ($memoryUsagePercent -gt 85) {
        Write-Host "[WARNING] High memory usage detected - may cause extension crashes" -ForegroundColor Red
    } else {
        Write-Host "[OK] Memory usage normal" -ForegroundColor Green
    }
    Write-Host ""
}

# Function to fix OpenSSL conflicts
function Fix-OpenSSLConflict {
    Write-Host "[FIX] Resolving OpenSSL conflicts..." -ForegroundColor Yellow
    
    # Check current metadata
    $metadataPath = "$env:USERPROFILE\.augment\metadata.json"
    if (Test-Path $metadataPath) {
        $currentMetadata = Get-Content $metadataPath | ConvertFrom-Json
        Write-Host "  Current OpenSSL path: $($currentMetadata.pathToOpenSSL)" -ForegroundColor Cyan
        
        # Try to find system OpenSSL
        $systemOpenSSL = Get-Command openssl -ErrorAction SilentlyContinue
        if ($systemOpenSSL) {
            Write-Host "  System OpenSSL found: $($systemOpenSSL.Source)" -ForegroundColor Green
            $newMetadata = @{ pathToOpenSSL = "openssl" }
            $newMetadata | ConvertTo-Json | Set-Content $metadataPath
            Write-Host "[OK] Updated metadata.json to use system OpenSSL" -ForegroundColor Green
        } else {
            Write-Host "[WARNING] System OpenSSL not found - keeping Git OpenSSL" -ForegroundColor Yellow
        }
    } else {
        Write-Host "[WARNING] Augment metadata.json not found" -ForegroundColor Yellow
    }
    Write-Host ""
}

# Function to check Node.js compatibility
function Test-NodeCompatibility {
    Write-Host "[DIAGNOSTIC] Checking Node.js compatibility..." -ForegroundColor Yellow
    
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Host "  Node.js version: $nodeVersion" -ForegroundColor Cyan
            
            # Check if version is compatible (should be 16+)
            $versionNumber = [Version]($nodeVersion -replace 'v', '')
            if ($versionNumber.Major -ge 16) {
                Write-Host "[OK] Node.js version compatible" -ForegroundColor Green
            } else {
                Write-Host "[WARNING] Node.js version may be too old (need 16+)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "[WARNING] Node.js not found or not in PATH" -ForegroundColor Red
        }
    } catch {
        Write-Host "[ERROR] Failed to check Node.js: $($_.Exception.Message)" -ForegroundColor Red
    }
    Write-Host ""
}

# Function to clean extension cache thoroughly
function Clear-ExtensionCache {
    Write-Host "[FIX] Clearing VS Code extension cache..." -ForegroundColor Yellow
    
    # Stop VS Code processes
    Get-Process -Name "*Code*" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 3
    
    # Clear various cache directories
    $cachePaths = @(
        "$env:APPDATA\Code - Insiders\CachedExtensions",
        "$env:APPDATA\Code - Insiders\CachedExtensionVSIXs",
        "$env:APPDATA\Code - Insiders\logs",
        "$env:APPDATA\Code - Insiders\exthost",
        "$env:TEMP\vscode-*"
    )
    
    foreach ($path in $cachePaths) {
        if (Test-Path $path) {
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "  Cleared: $path" -ForegroundColor Green
        }
    }
    
    Write-Host "[OK] Extension cache cleared" -ForegroundColor Green
    Write-Host ""
}

# Function to set conservative environment variables
function Set-AugmentEnvironment {
    Write-Host "[FIX] Setting conservative Augment environment..." -ForegroundColor Yellow
    
    # Set environment variables to reduce crash risk
    [Environment]::SetEnvironmentVariable("AUGMENT_DISABLE_REMOTE", "true", "User")
    [Environment]::SetEnvironmentVariable("AUGMENT_LOG_LEVEL", "error", "User")
    [Environment]::SetEnvironmentVariable("AUGMENT_MEMORY_LIMIT", "512", "User")
    [Environment]::SetEnvironmentVariable("NODE_OPTIONS", "--max-old-space-size=1024", "User")
    
    Write-Host "[OK] Environment variables set for stability" -ForegroundColor Green
    Write-Host ""
}

# Run all diagnostics and fixes
Write-Host "Running comprehensive diagnostics..." -ForegroundColor Cyan
Write-Host ""

Get-ExtensionCrashes
Test-SystemResources  
Fix-OpenSSLConflict
Test-NodeCompatibility
Clear-ExtensionCache
Set-AugmentEnvironment

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "DIAGNOSTICS COMPLETE" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart VS Code" -ForegroundColor White
Write-Host "2. Enable Augment extension" -ForegroundColor White  
Write-Host "3. Monitor for crashes" -ForegroundColor White
Write-Host "4. If crashes persist, check Event Viewer" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Open VS Code logs folder? (y/n)"
if ($choice -eq 'y') {
    Start-Process "$env:APPDATA\Code - Insiders\logs"
}
