@echo off
REM Fix Augment Extension HosREM Check if Augment extension is installed
echo Checking Augment extension status...
"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin\code-insiders.cmd" --list-extensions | findstr augment >nul
if %errorlevel%==0 (
    echo [OK] Augment extension found
) else (
    echo [WARNING] Augment extension not found
)s
REM Address OpenSSL conflicts and other common issues

echo ================================================
echo AUGMENT EXTENSION CRASH FIX
echo ================================================
echo.

echo [INFO] Checking current issues...
echo.

REM Fix VS Code CLI path first
echo [FIX 0] Setting up VS Code CLI access...
set "VSCODE_PATH=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin"
set "PATH=%VSCODE_PATH%;%PATH%"

REM Check if VS Code CLI is accessible
"%VSCODE_PATH%\code-insiders.cmd" --version >nul 2>&1
if %errorlevel%==0 (
    echo [OK] VS Code Insiders CLI found
    set "CODE_CMD=%VSCODE_PATH%\code-insiders.cmd"
) else (
    REM Try standard VS Code
    set "VSCODE_PATH=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin"
    "%VSCODE_PATH%\code.cmd" --version >nul 2>&1
    if %errorlevel%==0 (
        echo [OK] VS Code CLI found
        set "CODE_CMD=%VSCODE_PATH%\code.cmd"
    ) else (
        echo [ERROR] VS Code CLI not found - please install or fix PATH
        echo Expected locations:
        echo   - C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin
        echo   - C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin
        pause
        exit /b 1
    )
)
echo.

REM Check if Augment extension is installed
echo Checking Augment extension status...
"%CODE_CMD%" --list-extensions | findstr augment >nul
if %errorlevel%==0 (
    echo [OK] Augment extension found
) else (
    echo [WARNING] Augment extension not found
)
echo.

REM Fix 1: OpenSSL Path Conflict
echo [FIX 1] Resolving OpenSSL path conflicts...
echo Current metadata.json:
type "c:\Users\<USER>\.augment\metadata.json" 2>nul
echo.

REM Check for system OpenSSL
where openssl >nul 2>&1
if %errorlevel%==0 (
    echo [OK] System OpenSSL found
    where openssl
) else (
    echo [WARNING] No system OpenSSL found - installing...
    winget install --id=OpenSSL.OpenSSL -e --silent
)
echo.

REM Fix 2: Update metadata.json to use system OpenSSL
echo [FIX 2] Updating Augment metadata to use system OpenSSL...
if exist "c:\Users\<USER>\.augment\metadata.json" (
    echo {"pathToOpenSSL":"openssl"} > "c:\Users\<USER>\.augment\metadata.json"
    echo [OK] Updated metadata.json to use system OpenSSL
) else (
    echo [WARNING] metadata.json not found
)
echo.

REM Fix 3: Clear extension host cache
echo [FIX 3] Clearing extension host cache...
taskkill /f /im "Code - Insiders.exe" 2>nul
timeout /t 2 >nul
rmdir /s /q "%APPDATA%\Code - Insiders\CachedExtensions" 2>nul
rmdir /s /q "%APPDATA%\Code - Insiders\CachedExtensionVSIXs" 2>nul
echo [OK] Extension cache cleared
echo.

REM Fix 4: Reset SSH configuration
echo [FIX 4] Resetting SSH configuration...
if exist "c:\Users\<USER>\.augment\ssh" (
    echo [INFO] Backing up SSH config...
    copy "c:\Users\<USER>\.augment\ssh-keys\augment_remote_agent_key" "c:\Users\<USER>\.augment\ssh-keys\augment_remote_agent_key.backup" >nul 2>&1
    echo [OK] SSH key backed up
)
echo.

REM Fix 5: Set proper environment variables
echo [FIX 5] Setting environment variables...
setx AUGMENT_DISABLE_REMOTE "true" >nul
setx AUGMENT_LOG_LEVEL "error" >nul
echo [OK] Environment variables set
echo.

REM Fix 6: Check Node.js version
echo [FIX 6] Checking Node.js compatibility...
node --version 2>nul
if %errorlevel%==0 (
    echo [OK] Node.js found
) else (
    echo [WARNING] Node.js not found or not in PATH
)
echo.

echo ================================================
echo FIXES APPLIED - RESTART VS CODE TO TEST
echo ================================================
echo.
echo If crashes still occur:
echo 1. Disable remote features in Augment settings
echo 2. Reduce workspace indexing
echo 3. Check Windows Event Viewer for crash details
echo.
echo Press any key to open VS Code Event Logs folder...
pause >nul
start "" "%APPDATA%\Code - Insiders\logs"
exit /b 0
