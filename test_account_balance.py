#!/usr/bin/env python3
"""
Test Updated Account Balance Method
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import BotConfig

async def test_account_balance():
    """Test the updated account balance method"""
    print("TESTING UPDATED ACCOUNT BALANCE METHOD")
    print("=" * 50)
    
    try:
        # Load config
        config = BotConfig()
        
        # Initialize Bybit client
        print("Initializing Enhanced Bybit client...")
        client = EnhancedBybitClient(config)
        await client.initialize()
        
        print("SUCCESS: Client initialized")
        
        # Test account balance
        print("\nTesting account balance method...")
        balance_data = await client.get_account_balance()
        
        if balance_data:
            print("SUCCESS: Account balance retrieved!")
            print(f"Account Type: {balance_data.get('account_type', 'Unknown')}")
            print(f"Margin Mode: {balance_data.get('margin_mode', 'Unknown')}")
            print(f"Total Equity: ${balance_data.get('total_equity', 0):.2f}")
            print(f"Available Balance: ${balance_data.get('available_balance', 0):.2f}")
            print(f"Unrealized PnL: ${balance_data.get('unrealized_pnl', 0):.2f}")
            print(f"Used Margin: ${balance_data.get('used_margin', 0):.2f}")
            
            # Show coin balances
            coins = balance_data.get('coins', {})
            if coins:
                print("\nCoin Balances:")
                for coin, data in coins.items():
                    if data['total'] > 0:  # Only show coins with balance
                        print(f"  {coin}: {data['total']:.8f} (Available: {data['available']:.8f})")
            else:
                print("No coin balance data available")
            
            # Test account info method
            print("\nTesting account info method...")
            account_info = await client.get_account_info()
            
            if account_info:
                print("SUCCESS: Account info retrieved!")
                print(f"Total Equity: ${account_info.get('totalEquity', '0')}")
                print(f"Available Balance: ${account_info.get('totalAvailableBalance', '0')}")
                print(f"Account Type: {account_info.get('accountType', 'Unknown')}")
                print(f"Margin Mode: {account_info.get('marginMode', 'Unknown')}")
                
                return True
            else:
                print("ERROR: Account info method failed")
                return False
        else:
            print("ERROR: Account balance method failed")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'client' in locals():
            await client.close()

if __name__ == "__main__":
    result = asyncio.run(test_account_balance())
    
    print("\n" + "=" * 50)
    if result:
        print("RESULT: ACCOUNT BALANCE TEST SUCCESS!")
        print("The updated account balance method works correctly")
        print("Your API credentials are properly configured")
    else:
        print("RESULT: ACCOUNT BALANCE TEST FAILED")
        print("There may be an issue with the implementation")
    print("=" * 50)
