#!/usr/bin/env python3
"""
Emergency SQLite Database Fix Script
Fixes the multi-statement SQL execution issue that's causing SQLite errors
"""

import os
import sys
import sqlite3
import asyncio
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def kill_python_processes():
    """Kill all Python processes to clear any stuck processes"""
    try:
        if os.name == 'nt':  # Windows
            os.system('taskkill /F /IM python.exe /T 2>nul')
            os.system('taskkill /F /IM pythonw.exe /T 2>nul')
            logger.info("Killed all Python processes on Windows")
        else:  # Unix/Linux
            os.system('pkill -f python')
            logger.info("Killed all Python processes on Unix/Linux")
    except Exception as e:
        logger.warning(f"Could not kill Python processes: {e}")

def create_clean_sqlite_database():
    """Create a clean SQLite database with all required tables"""
    try:
        db_path = "bybit_trading_bot.db"
        
        # Remove existing database if it exists
        if os.path.exists(db_path):
            os.remove(db_path)
            logger.info(f"Removed existing database: {db_path}")
        
        # Create new SQLite database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create tables one by one (SQLite compatible)
        tables = [
            """
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                side VARCHAR(10) NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                order_id VARCHAR(50),
                exchange_order_id VARCHAR(100) UNIQUE,
                status VARCHAR(20) DEFAULT 'completed',
                strategy VARCHAR(50),
                position_id INTEGER,
                entry_price REAL,
                exit_price REAL,
                profit_loss REAL DEFAULT 0.0,
                fees REAL DEFAULT 0.0,
                executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                extra_data TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS trading_memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                memory_id VARCHAR(100) UNIQUE,
                pattern_type VARCHAR(50),
                importance VARCHAR(20),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                market_conditions TEXT,
                strategy VARCHAR(50),
                action VARCHAR(20),
                outcome TEXT,
                metadata TEXT,
                pattern_hash VARCHAR(100),
                confidence REAL DEFAULT 0.5,
                usage_count INTEGER DEFAULT 1,
                last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN DEFAULT FALSE
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS meta_cognition_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                awareness_level REAL,
                cognitive_load REAL,
                system_health REAL,
                adaptation_score REAL,
                learning_efficiency REAL,
                metrics_data TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS code_evolution_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_path VARCHAR(500),
                change_type VARCHAR(50),
                improvement_score REAL,
                performance_impact REAL,
                change_description TEXT,
                change_data TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS profit_generation_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                strategy_name VARCHAR(100),
                symbol VARCHAR(20),
                profit_amount REAL,
                profit_percentage REAL,
                execution_time_ms INTEGER,
                method VARCHAR(100),
                details TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                cpu_usage REAL,
                memory_usage REAL,
                disk_usage REAL,
                error_rate REAL,
                component_status TEXT
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS bot_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key VARCHAR(100) UNIQUE NOT NULL,
                config_value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """
        ]
        
        # Execute each table creation individually
        for i, table_sql in enumerate(tables):
            try:
                cursor.execute(table_sql.strip())
                logger.info(f"Created table {i+1}/{len(tables)}")
            except Exception as e:
                logger.error(f"Failed to create table {i+1}: {e}")
                continue
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(executed_at)",
            "CREATE INDEX IF NOT EXISTS idx_trading_memories_timestamp ON trading_memories(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_meta_cognition_timestamp ON meta_cognition_metrics(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_code_evolution_timestamp ON code_evolution_history(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_profit_log_timestamp ON profit_generation_log(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health(timestamp)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                logger.warning(f"Failed to create index: {e}")
        
        # Insert initial configuration
        initial_config = [
            ("system_initialized", '{"value": true, "timestamp": "' + str(asyncio.get_event_loop().time()) + '"}'),
            ("database_type", '{"value": "sqlite", "compatible": true}'),
            ("profit_target_daily", '{"value": 15000.0, "currency": "USD"}'),
            ("profit_target_hourly", '{"value": 625.0, "currency": "USD"}')
        ]
        
        for key, value in initial_config:
            try:
                cursor.execute(
                    "INSERT OR REPLACE INTO bot_config (config_key, config_value) VALUES (?, ?)",
                    (key, value)
                )
            except Exception as e:
                logger.warning(f"Failed to insert config {key}: {e}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"SUCCESS: Clean SQLite database created at {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create clean SQLite database: {e}")
        return False

def main():
    """Main function to fix the SQLite database issue"""
    logger.info("STARTING Emergency SQLite Database Fix...")
    
    # Step 1: Kill any stuck Python processes
    logger.info("Step 1: Killing stuck Python processes...")
    kill_python_processes()
    
    # Step 2: Create clean SQLite database
    logger.info("Step 2: Creating clean SQLite database...")
    success = create_clean_sqlite_database()
    
    if success:
        logger.info("SUCCESS: SQLite database issue fixed!")
        logger.info("The system should now be able to start without multi-statement SQL errors")
        logger.info("You can now try running: python main.py")
    else:
        logger.error("FAILED: Could not fix SQLite database issue")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
