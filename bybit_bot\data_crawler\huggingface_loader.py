"""
HuggingFace Integration for Bulk Data Loading
Provides access to financial and cryptocurrency datasets
"""

import asyncio
import json
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from datasets import load_dataset
from huggingface_hub import HfA<PERSON>, list_datasets
import numpy as np

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class HuggingFaceDataLoader:
    """
    HuggingFace integration for loading financial and crypto datasets
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # HuggingFace API
        self.hf_api = HfApi()
        
        # Target datasets for crypto/financial data
        self.target_datasets = [
            # Financial datasets
            "financial_phrasebank",
            "zeroshot/twitter-financial-news-sentiment",
            "ElKulako/cryptos", 
            "AdiOO7/financial-news-sentiment",
            "pauri32/fiqa-sentiment-classification",
            
            # Market data
            "lukebarousse/data_jobs",
            "datasets/financial_phrasebank",
            "bharatm/financial-sentiments",
            
            # Crypto specific
            "DeBeesTalk/crypto_fear_greed_index",
            "prasant3739/crypto-news-sentiment",
        ]
        
        # Economic indicators datasets
        self.economic_datasets = [
            "economic_indicators",
            "macroeconomic_data",
            "inflation_data",
            "gdp_data"
        ]
        
        self.loaded_datasets = {}
        
    async def initialize(self):
        """Initialize the HuggingFace data loader"""
        try:
            self.logger.info("🤗 Initializing HuggingFace Data Loader...")
            
            # Test connection and discover datasets
            await self._discover_relevant_datasets()
            
            self.logger.info("✅ HuggingFace Data Loader initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize HuggingFace Data Loader: {e}")
            raise
    
    async def load_financial_sentiment_data(self) -> pd.DataFrame:
        """Load financial sentiment datasets"""
        try:
            self.logger.info("Loading financial sentiment datasets...")
            
            all_sentiment_data = []
            
            # Load financial phrasebank
            try:
                dataset = load_dataset("financial_phrasebank", "sentences_allagree")
                df = pd.DataFrame(dataset['train'])
                df['source'] = 'financial_phrasebank'
                df['category'] = 'sentiment'
                all_sentiment_data.append(df)
                self.logger.info(f"SUCCESS: Loaded {len(df)} records from financial_phrasebank")
            except Exception as e:
                self.logger.warning(f"Could not load financial_phrasebank: {e}")
            
            # Load crypto sentiment data
            crypto_sentiment_datasets = [
                "prasant3739/crypto-news-sentiment",
                "AdiOO7/financial-news-sentiment"
            ]
            
            for dataset_name in crypto_sentiment_datasets:
                try:
                    dataset = load_dataset(dataset_name)
                    df = pd.DataFrame(dataset['train'] if 'train' in dataset else dataset[list(dataset.keys())[0]])
                    df['source'] = dataset_name
                    df['category'] = 'crypto_sentiment'
                    all_sentiment_data.append(df)
                    self.logger.info(f"✅ Loaded {len(df)} records from {dataset_name}")
                except Exception as e:
                    self.logger.warning(f"Could not load {dataset_name}: {e}")
            
            # Combine all sentiment data
            if all_sentiment_data:
                combined_df = pd.concat(all_sentiment_data, ignore_index=True)
                combined_df['loaded_at'] = datetime.now(timezone.utc)
                
                # Store in database
                await self._bulk_load_to_postgres(combined_df, 'sentiment_data')
                
                self.logger.info(f"📊 Loaded total {len(combined_df)} sentiment records")
                return combined_df
            else:
                self.logger.warning("No sentiment data could be loaded")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Error loading financial sentiment data: {e}")
            return pd.DataFrame()
    
    async def load_market_data_datasets(self) -> pd.DataFrame:
        """Load market and price datasets"""
        try:
            self.logger.info("📈 Loading market data datasets...")
            
            all_market_data = []
            
            # Try to load crypto datasets
            crypto_datasets = [
                "ElKulako/cryptos",
                "DeBeesTalk/crypto_fear_greed_index"
            ]
            
            for dataset_name in crypto_datasets:
                try:
                    dataset = load_dataset(dataset_name)
                    df = pd.DataFrame(dataset['train'] if 'train' in dataset else dataset[list(dataset.keys())[0]])
                    df['source'] = dataset_name
                    df['category'] = 'market_data'
                    all_market_data.append(df)
                    self.logger.info(f"✅ Loaded {len(df)} records from {dataset_name}")
                except Exception as e:
                    self.logger.warning(f"Could not load {dataset_name}: {e}")
            
            # Create synthetic market indicators if no real data available
            if not all_market_data:
                synthetic_df = await self._create_synthetic_market_data()
                all_market_data.append(synthetic_df)
            
            # Combine all market data
            if all_market_data:
                combined_df = pd.concat(all_market_data, ignore_index=True)
                combined_df['loaded_at'] = datetime.now(timezone.utc)
                
                # Store in database
                await self._bulk_load_to_postgres(combined_df, 'market_datasets')
                
                self.logger.info(f"📈 Loaded total {len(combined_df)} market data records")
                return combined_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Error loading market data: {e}")
            return pd.DataFrame()
    
    async def load_economic_indicators(self) -> pd.DataFrame:
        """Load economic indicators datasets"""
        try:
            self.logger.info("🏦 Loading economic indicators...")
            
            # Create synthetic economic data (since real datasets might not be available)
            economic_df = await self._create_synthetic_economic_data()
            
            # Store in database
            await self._bulk_load_to_postgres(economic_df, 'economic_indicators')
            
            self.logger.info(f"🏦 Loaded {len(economic_df)} economic indicator records")
            return economic_df
            
        except Exception as e:
            self.logger.error(f"Error loading economic indicators: {e}")
            return pd.DataFrame()
    
    async def load_trading_patterns_data(self) -> pd.DataFrame:
        """Load trading patterns and strategy datasets"""
        try:
            self.logger.info("🔄 Loading trading patterns data...")
            
            # Create synthetic trading patterns data
            patterns_df = await self._create_trading_patterns_data()
            
            # Store in database
            await self._bulk_load_to_postgres(patterns_df, 'trading_patterns')
            
            self.logger.info(f"🔄 Loaded {len(patterns_df)} trading pattern records")
            return patterns_df
            
        except Exception as e:
            self.logger.error(f"Error loading trading patterns: {e}")
            return pd.DataFrame()
    
    async def bulk_load_all_datasets(self):
        """Load all available datasets"""
        try:
            self.logger.info("🚀 Starting bulk data loading from HuggingFace...")
            
            # Load datasets concurrently
            tasks = [
                self.load_financial_sentiment_data(),
                self.load_market_data_datasets(),
                self.load_economic_indicators(),
                self.load_trading_patterns_data()
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            total_records = 0
            for i, result in enumerate(results):
                if isinstance(result, pd.DataFrame):
                    total_records += len(result)
                    self.logger.info(f"✅ Dataset {i+1} loaded successfully")
                else:
                    self.logger.error(f"❌ Dataset {i+1} failed: {result}")
            
            self.logger.info(f"🎯 Bulk loading complete! Total {total_records} records loaded")
            
        except Exception as e:
            self.logger.error(f"Error in bulk loading: {e}")
    
    async def get_sentiment_training_data(self, limit: int = 10000) -> Tuple[np.ndarray, np.ndarray]:
        """Get processed sentiment data for ML training"""
        try:
            query = """
            SELECT text, sentiment, sentiment_score 
            FROM sentiment_data 
            WHERE text IS NOT NULL AND sentiment IS NOT NULL
            LIMIT $1
            """
            
            rows = await self.db.fetch_all(query, limit)
            
            if not rows:
                self.logger.warning("No sentiment training data available")
                return np.array([]), np.array([])
            
            texts = [row['text'] for row in rows]
            sentiments = [row['sentiment'] for row in rows]
            
            # Convert sentiment labels to numeric
            sentiment_mapping = {'positive': 1, 'neutral': 0, 'negative': -1}
            numeric_sentiments = [sentiment_mapping.get(s, 0) for s in sentiments]
            
            return np.array(texts), np.array(numeric_sentiments)
            
        except Exception as e:
            self.logger.error(f"Error getting sentiment training data: {e}")
            return np.array([]), np.array([])
    
    async def get_market_features_data(self, symbol: str = 'BTCUSDT', days: int = 365) -> pd.DataFrame:
        """Get market features data for ML training"""
        try:
            query = """
            SELECT * FROM market_datasets 
            WHERE symbol = $1 
            AND timestamp > NOW() - INTERVAL '%s days'
            ORDER BY timestamp DESC
            """ % days
            
            rows = await self.db.fetch_all(query, symbol)
            
            if not rows:
                self.logger.warning(f"No market data available for {symbol}")
                return pd.DataFrame()
            
            df = pd.DataFrame([dict(row) for row in rows])
            return df
            
        except Exception as e:
            self.logger.error(f"Error getting market features data: {e}")
            return pd.DataFrame()
    
    async def _discover_relevant_datasets(self):
        """Discover relevant datasets on HuggingFace"""
        try:
            # Search for financial and crypto related datasets
            search_terms = ["financial", "crypto", "trading", "sentiment", "market"]
            
            discovered_datasets = []
            
            for term in search_terms:
                try:
                    datasets = list_datasets(search=term, limit=20)
                    for dataset in datasets:
                        if any(keyword in dataset.id.lower() for keyword in ["financial", "crypto", "trading", "sentiment"]):
                            discovered_datasets.append(dataset.id)
                except Exception as e:
                    self.logger.warning(f"Error searching for {term} datasets: {e}")
            
            # Update target datasets with discoveries
            self.target_datasets.extend(discovered_datasets[:10])  # Add top 10 discoveries
            self.target_datasets = list(set(self.target_datasets))  # Remove duplicates
            
            self.logger.info(f"📋 Discovered {len(discovered_datasets)} relevant datasets")
            
        except Exception as e:
            self.logger.error(f"Error discovering datasets: {e}")
    
    async def _bulk_load_to_postgres(self, df: pd.DataFrame, table_name: str):
        """Bulk load DataFrame to PostgreSQL"""
        try:
            if df.empty:
                self.logger.warning(f"No data to load for table {table_name}")
                return
            
            # Create table if not exists
            await self._create_table_if_not_exists(table_name, df)
            
            # Convert DataFrame to records
            records = df.to_dict('records')
            
            # Bulk insert
            columns = list(df.columns)
            placeholders = ', '.join([f'${i+1}' for i in range(len(columns))])
            
            query = f"""
            INSERT INTO {table_name} ({', '.join(columns)})
            VALUES ({placeholders})
            ON CONFLICT DO NOTHING
            """
            
            # Insert in batches
            batch_size = 1000
            for i in range(0, len(records), batch_size):
                batch = records[i:i+batch_size]
                values = [[record[col] for col in columns] for record in batch]
                
                try:
                    await self.db.execute_many(query, values)
                except Exception as e:
                    self.logger.error(f"Error inserting batch {i//batch_size + 1}: {e}")
            
            self.logger.info(f"✅ Bulk loaded {len(records)} records to {table_name}")
            
        except Exception as e:
            self.logger.error(f"Error bulk loading to {table_name}: {e}")
    
    async def _create_table_if_not_exists(self, table_name: str, df: pd.DataFrame):
        """Create table schema based on DataFrame"""
        try:
            # Generate CREATE TABLE statement
            columns_def = []
            
            for col, dtype in df.dtypes.items():
                if dtype == 'object':
                    col_type = 'TEXT'
                elif dtype in ['int64', 'int32']:
                    col_type = 'INTEGER'
                elif dtype in ['float64', 'float32']:
                    col_type = 'REAL'
                elif dtype == 'datetime64[ns]':
                    col_type = 'TIMESTAMP'
                elif dtype == 'bool':
                    col_type = 'BOOLEAN'
                else:
                    col_type = 'TEXT'
                
                columns_def.append(f"{col} {col_type}")
            
            create_query = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                {', '.join(columns_def)}
            )
            """
            
            await self.db.execute(create_query)
            
        except Exception as e:
            self.logger.error(f"Error creating table {table_name}: {e}")
    
    async def _create_synthetic_market_data(self) -> pd.DataFrame:
        """Create synthetic market data for testing"""
        try:
            # Generate synthetic crypto market data
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
            
            data = []
            base_date = datetime.now(timezone.utc) - timedelta(days=365)
            
            for symbol in symbols:
                for i in range(365):
                    date = base_date + timedelta(days=i)
                    
                    # Generate synthetic OHLCV data
                    base_price = 50000 if 'BTC' in symbol else 3000 if 'ETH' in symbol else 1.0
                    price_variation = np.random.normal(0, 0.02)  # 2% daily volatility
                    
                    open_price = base_price * (1 + price_variation)
                    high_price = open_price * (1 + abs(np.random.normal(0, 0.01)))
                    low_price = open_price * (1 - abs(np.random.normal(0, 0.01)))
                    close_price = open_price * (1 + np.random.normal(0, 0.015))
                    volume = np.random.uniform(1000000, 10000000)
                    
                    data.append({
                        'symbol': symbol,
                        'timestamp': date,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume,
                        'volatility': abs(price_variation),
                        'rsi': np.random.uniform(20, 80),
                        'macd': np.random.normal(0, 1),
                        'source': 'synthetic',
                        'category': 'ohlcv'
                    })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"Error creating synthetic market data: {e}")
            return pd.DataFrame()
    
    async def _create_synthetic_economic_data(self) -> pd.DataFrame:
        """Create synthetic economic indicators data"""
        try:
            indicators = ['GDP', 'INFLATION', 'UNEMPLOYMENT', 'INTEREST_RATE', 'DXY']
            countries = ['US', 'EU', 'JP', 'UK', 'CN']
            
            data = []
            base_date = datetime.now(timezone.utc) - timedelta(days=365*2)  # 2 years of data
            
            for indicator in indicators:
                for country in countries:
                    for i in range(24):  # Monthly data for 2 years
                        date = base_date + timedelta(days=i*30)
                        
                        # Generate realistic values
                        if indicator == 'GDP':
                            value = np.random.uniform(1.0, 4.0)  # GDP growth %
                        elif indicator == 'INFLATION':
                            value = np.random.uniform(0.5, 8.0)  # Inflation %
                        elif indicator == 'UNEMPLOYMENT':
                            value = np.random.uniform(3.0, 15.0)  # Unemployment %
                        elif indicator == 'INTEREST_RATE':
                            value = np.random.uniform(0.0, 5.0)  # Interest rate %
                        else:  # DXY
                            value = np.random.uniform(95.0, 105.0)  # DXY index
                        
                        data.append({
                            'indicator': indicator,
                            'country': country,
                            'date': date,
                            'value': value,
                            'unit': '%' if indicator != 'DXY' else 'index',
                            'source': 'synthetic',
                            'category': 'economic'
                        })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"Error creating synthetic economic data: {e}")
            return pd.DataFrame()
    
    async def _create_trading_patterns_data(self) -> pd.DataFrame:
        """Create trading patterns and signals data"""
        try:
            patterns = ['DOJI', 'HAMMER', 'SHOOTING_STAR', 'BULLISH_ENGULFING', 'BEARISH_ENGULFING']
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
            
            data = []
            base_date = datetime.now(timezone.utc) - timedelta(days=180)
            
            for i in range(1000):  # 1000 pattern instances
                date = base_date + timedelta(hours=np.random.randint(0, 180*24))
                symbol = np.random.choice(symbols)
                pattern = np.random.choice(patterns)
                
                # Generate pattern characteristics
                strength = np.random.uniform(0.3, 1.0)
                success_rate = np.random.uniform(0.4, 0.9)
                
                data.append({
                    'symbol': symbol,
                    'timestamp': date,
                    'pattern': pattern,
                    'strength': strength,
                    'success_rate': success_rate,
                    'market_condition': np.random.choice(['bullish', 'bearish', 'sideways']),
                    'volume_spike': np.random.choice([True, False]),
                    'timeframe': np.random.choice(['1h', '4h', '1d']),
                    'source': 'synthetic',
                    'category': 'pattern'
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"Error creating trading patterns data: {e}")
            return pd.DataFrame()
