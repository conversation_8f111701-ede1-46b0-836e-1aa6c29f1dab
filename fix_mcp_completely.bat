@echo off
echo [INFO] Comprehensive MCP Server Fix - ALL SERVERS
echo =============================================

REM 1. Clean all NPM caches completely
echo [STEP 1] Cleaning all NPM caches...
if exist "E:\The_real_deal_copy\Bybit_Bot\BOT\node_env\.npm-cache" (
    rmdir /s /q "E:\The_real_deal_copy\Bybit_Bot\BOT\node_env\.npm-cache" 2>nul
    echo [OK] Local cache cleared
)

if exist "%APPDATA%\npm-cache" (
    rmdir /s /q "%APPDATA%\npm-cache" 2>nul
    echo [OK] Global cache cleared
)

if exist "%LOCALAPPDATA%\npm-cache" (
    rmdir /s /q "%LOCALAPPDATA%\npm-cache" 2>nul
    echo [OK] Local AppData cache cleared
)

REM 2. Set proper NPM cache directory
echo [STEP 2] Setting clean NPM cache directory...
set npm_config_cache=C:\Users\<USER>\AppData\Local\npm-cache
mkdir "%npm_config_cache%" 2>nul
npm config set cache "%npm_config_cache%"
echo [OK] NPM cache directory set to: %npm_config_cache%

REM 3. Install ALL MCP packages globally with clean cache
echo [STEP 3] Installing ALL MCP packages with clean cache...

echo [3.1] Installing filesystem MCP server...
npm install -g @modelcontextprotocol/server-filesystem --cache "%npm_config_cache%"
if %errorlevel% equ 0 (
    echo [OK] Filesystem MCP server installed
) else (
    echo [ERROR] Failed to install filesystem MCP server
)

echo [3.2] Installing github MCP server...
npm install -g @modelcontextprotocol/server-github --cache "%npm_config_cache%"
if %errorlevel% equ 0 (
    echo [OK] GitHub MCP server installed
) else (
    echo [ERROR] Failed to install GitHub MCP server
)

echo [3.3] Installing sequential thinking MCP server...
npm install -g @modelcontextprotocol/server-sequentialthinking --cache "%npm_config_cache%"
if %errorlevel% equ 0 (
    echo [OK] Sequential thinking MCP server installed
) else (
    echo [ERROR] Failed to install sequential thinking MCP server
)

echo [3.4] Installing web search MCP server...
npm install -g WebSearch-MCP --cache "%npm_config_cache%"
if %errorlevel% equ 0 (
    echo [OK] Web search MCP server installed
) else (
    echo [ERROR] Failed to install web search MCP server
)

REM 4. Test MCP server installations
echo [STEP 4] Testing MCP server installations...
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Node.js found
    node --version
) else (
    echo [ERROR] Node.js not found in PATH
)

echo [4.1] Verifying installed MCP packages...
npm list -g @modelcontextprotocol/server-filesystem 2>nul | findstr "server-filesystem"
npm list -g @modelcontextprotocol/server-github 2>nul | findstr "server-github"
npm list -g @modelcontextprotocol/server-sequentialthinking 2>nul | findstr "server-sequentialthinking"
npm list -g WebSearch-MCP 2>nul | findstr "WebSearch-MCP"

REM 5. Check Python MCP servers
echo [STEP 5] Checking Python MCP servers...
if exist "E:\The_real_deal_copy\Bybit_Bot\BOT\mcp_server_bybit.py" (
    echo [OK] Bybit trading MCP server file exists
) else (
    echo [ERROR] Bybit trading MCP server not found
)

if exist "E:\The_real_deal_copy\Bybit_Bot\BOT\bybit_bot\mcp" (
    echo [OK] MCP module directory exists
) else (
    echo [ERROR] MCP module directory not found
)

"E:\Miniconda\envs\bybit-trader\python.exe" -c "import sys; print(f'Python {sys.version}')"

REM 6. Test NPX command with clean cache
echo [STEP 6] Testing NPX with clean cache...
npx --version
echo [INFO] Testing filesystem server with clean cache...
timeout /t 3 >nul
echo [OK] NPX working with clean cache

echo.
echo ===============================================
echo [SUCCESS] ALL MCP SERVERS FIXED AND INSTALLED
echo ===============================================
echo [INFO] All MCP servers should now work without cache errors
echo [INFO] Restart VS Code Insiders to apply changes
echo.
pause
