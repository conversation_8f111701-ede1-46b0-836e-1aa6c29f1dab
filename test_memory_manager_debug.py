#!/usr/bin/env python3
"""
Test PersistentMemoryManager initialization to debug the database manager issue
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_memory_manager_debug():
    """Test PersistentMemoryManager with debug output"""
    print("TESTING: PersistentMemoryManager Debug")
    print("=" * 50)
    
    try:
        # Import required classes
        from bybit_bot.database.connection import DatabaseManager
        from bybit_bot.core.config import BotConfig
        from bybit_bot.ai.memory_manager import PersistentMemoryManager
        
        print("SUCCESS: All imports successful")
        
        # Create config and database manager
        config = BotConfig()
        print("SUCCESS: Config created")
        
        db_manager = DatabaseManager(config)
        print(f"SUCCESS: DatabaseManager created, type: {type(db_manager)}")
        
        # Check if database manager has the required methods
        methods_to_check = ['fetch_all', 'fetch_one', 'execute_sql']
        for method in methods_to_check:
            if hasattr(db_manager, method):
                print(f"SUCCESS: DatabaseManager has {method} method")
            else:
                print(f"ERROR: DatabaseManager missing {method} method")
        
        # Try to initialize database manager
        try:
            await db_manager.initialize()
            print("SUCCESS: DatabaseManager initialized")
        except Exception as e:
            print(f"WARNING: DatabaseManager initialization failed: {e}")
            print("Continuing with uninitialized database manager...")
        
        # Create PersistentMemoryManager
        print("Creating PersistentMemoryManager...")
        memory_manager = PersistentMemoryManager(config, db_manager)
        print(f"SUCCESS: PersistentMemoryManager created")
        
        # Check what self.db points to
        print(f"memory_manager.db type: {type(memory_manager.db)}")
        print(f"memory_manager.db is db_manager: {memory_manager.db is db_manager}")
        print(f"memory_manager.db has fetch_all: {hasattr(memory_manager.db, 'fetch_all')}")
        
        # Try to initialize memory manager
        try:
            await memory_manager.initialize()
            print("SUCCESS: PersistentMemoryManager initialized")
        except Exception as e:
            print(f"ERROR: PersistentMemoryManager initialization failed: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"ERROR: Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_memory_manager_debug())
