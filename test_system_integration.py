#!/usr/bin/env python3
"""
COMPREHENSIVE SYSTEM INTEGRATION TEST
Tests all components for 100% operational status
"""

import asyncio
import main

async def test_system():
    print('STARTING COMPREHENSIVE SYSTEM INTEGRATION TEST...')
    bot = main.BybitTradingBotSystem()
    print('Bot system created successfully')
    
    # Test initialization
    print('Testing system initialization...')
    success = await bot.initialize_all_systems()
    print(f'System initialization: {"SUCCESS" if success else "FAILED"}')
    
    # Check critical components
    components_status = {}
    components_status['strategy_manager'] = bot.strategy_manager is not None
    components_status['performance_analyzer'] = bot.performance_analyzer is not None
    components_status['profit_target_enforcer'] = bot.profit_target_enforcer is not None
    components_status['bybit_client'] = bot.bybit_client is not None
    components_status['database_manager'] = bot.database_manager is not None
    components_status['memory_manager'] = hasattr(bot.strategy_manager, 'memory_manager') if bot.strategy_manager else False
    
    print('COMPONENT STATUS:')
    for component, status in components_status.items():
        print(f'  {component}: {"ACTIVE" if status else "INACTIVE"}')
    
    # Test profit reporting
    print('Testing profit reporting system...')
    await bot.generate_4hour_profit_report()
    print('4-hour report generated successfully')
    
    all_active = all(components_status.values())
    print(f'INTEGRATION TEST RESULT: {"100% SUCCESS" if all_active else "PARTIAL SUCCESS"}')
    
    await bot.shutdown()
    return all_active

if __name__ == "__main__":
    result = asyncio.run(test_system())
    print(f'FINAL RESULT: {"COMPLETE SUCCESS" if result else "NEEDS ATTENTION"}')
