#!/usr/bin/env python3
"""
Test Symbol Fixes - Verify the symbol validation fixes are working
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_symbol_fixes():
    with open("symbol_test_output.txt", "w") as f:
        def log(msg):
            print(msg)
            f.write(msg + "\n")
            f.flush()
        
        log("=== SYMBOL FIX VERIFICATION ===")
        log(f"Time: {datetime.now()}")
        
        try:
            log("1. Importing Bybit client...")
            from bybit_bot.exchange.bybit_client import BybitClient
            log("   SUCCESS: BybitClient imported")
            
            log("2. Creating config and client instance...")
            from bybit_bot.core.config import BotConfig
            config = BotConfig()
            client = BybitClient(config)
            log("   SUCCESS: Client created")

            log("3. Initializing client...")
            await client.initialize()
            log("   SUCCESS: Client initialized")
            
            log("4. Testing BTCUSDT spot klines...")
            klines = await client.get_klines("spot", "BTCUSDT", "1", 1)
            if klines.get('retCode') == 0:
                price = klines['result']['list'][0][4]
                log(f"   SUCCESS: BTCUSDT price = ${price}")
            else:
                log(f"   FAILED: {klines}")

            log("5. Testing ETHUSDT spot klines...")
            klines = await client.get_klines("spot", "ETHUSDT", "1", 1)
            if klines.get('retCode') == 0:
                price = klines['result']['list'][0][4]
                log(f"   SUCCESS: ETHUSDT price = ${price}")
            else:
                log(f"   FAILED: {klines}")

            log("6. Testing get_market_data method...")
            market_data = await client.get_market_data("BTCUSDT", "1", 5)
            if market_data and len(market_data) > 0:
                log(f"   SUCCESS: Got {len(market_data)} candles")
                log(f"   Latest close: ${market_data[0]['close']}")
            else:
                log("   FAILED: No market data returned")

            log("7. Testing account balance...")
            balance = await client.get_account_balance()
            if balance.get('retCode') == 0:
                log("   SUCCESS: Account accessible")
                for account in balance['result']['list']:
                    for coin in account.get('coin', []):
                        if coin['coin'] == 'USDT' and float(coin.get('walletBalance', 0)) > 0:
                            log(f"   Balance: {coin['walletBalance']} USDT")
            else:
                log(f"   FAILED: {balance}")
                
            await client.close()
            log("8. Client closed successfully")
            
            log("\n=== SYMBOL FIX VERIFICATION COMPLETE ===")
            log("If all tests show SUCCESS, the symbol fixes are working correctly.")
            
        except Exception as e:
            log(f"ERROR: {e}")
            import traceback
            log(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(test_symbol_fixes())
