"""
MCP System Verification Script
Verifies all MCPs are properly configured and active for both system and Copilot use
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MCPSystemVerification:
    """Comprehensive MCP system verification"""
    
    def __init__(self):
        self.mcp_config_path = Path("C:/Users/<USER>/AppData/Roaming/Code - Insiders/User/mcp.json")
        self.required_mcps = [
            'bybit-trading',
            'sequentialthinking', 
            'pylance',
            'memory',
            'postgres',
            'mssql'
        ]
        self.verification_results = {}
        
    async def verify_complete_system(self) -> Dict[str, Any]:
        """Verify complete MCP system configuration and functionality"""
        logger.info("🔍 Starting comprehensive MCP system verification...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'config_verification': {},
            'mcp_status': {},
            'copilot_integration': {},
            'live_data_verification': {},
            'recommendations': []
        }
        
        try:
            # 1. Verify MCP configuration file
            results['config_verification'] = await self._verify_config_file()
            
            # 2. Verify each MCP server
            results['mcp_status'] = await self._verify_mcp_servers()
            
            # 3. Verify Copilot integration
            results['copilot_integration'] = await self._verify_copilot_integration()
            
            # 4. Verify live data access
            results['live_data_verification'] = await self._verify_live_data_access()
            
            # 5. Generate recommendations
            results['recommendations'] = await self._generate_recommendations(results)
            
            # 6. Determine overall status
            results['overall_status'] = self._determine_overall_status(results)
            
            logger.info(f"✅ Verification complete. Overall status: {results['overall_status']}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            results['overall_status'] = 'failed'
            results['error'] = str(e)
            return results
    
    async def _verify_config_file(self) -> Dict[str, Any]:
        """Verify MCP configuration file exists and is properly formatted"""
        logger.info("📋 Verifying MCP configuration file...")
        
        config_status = {
            'file_exists': False,
            'valid_json': False,
            'required_sections': {},
            'server_count': 0,
            'critical_mcps_configured': []
        }
        
        try:
            if self.mcp_config_path.exists():
                config_status['file_exists'] = True
                
                with open(self.mcp_config_path, 'r') as f:
                    config = json.load(f)
                    config_status['valid_json'] = True
                    
                    # Check required sections
                    config_status['required_sections']['mcpServers'] = 'mcpServers' in config
                    config_status['required_sections']['copilotIntegration'] = 'copilotIntegration' in config
                    config_status['required_sections']['logging'] = 'logging' in config
                    config_status['required_sections']['performance'] = 'performance' in config
                    
                    # Check MCP servers
                    if 'mcpServers' in config:
                        servers = config['mcpServers']
                        config_status['server_count'] = len(servers)
                        
                        for mcp in self.required_mcps:
                            if mcp in servers:
                                config_status['critical_mcps_configured'].append(mcp)
                    
                    logger.info(f"✅ Configuration file valid. {config_status['server_count']} servers configured")
            else:
                logger.error(f"❌ MCP configuration file not found: {self.mcp_config_path}")
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON in configuration file: {e}")
        except Exception as e:
            logger.error(f"❌ Error reading configuration file: {e}")
            
        return config_status
    
    async def _verify_mcp_servers(self) -> Dict[str, Any]:
        """Verify each MCP server configuration and status"""
        logger.info("🔧 Verifying MCP server configurations...")
        
        server_status = {}
        
        for mcp_name in self.required_mcps:
            logger.info(f"   Checking {mcp_name}...")
            
            status = {
                'configured': False,
                'priority': 'unknown',
                'timeout': 0,
                'copilot_enabled': False,
                'critical_for_trading': False,
                'expected_functionality': []
            }
            
            try:
                # Load config and check specific server
                with open(self.mcp_config_path, 'r') as f:
                    config = json.load(f)
                    
                if mcp_name in config.get('mcpServers', {}):
                    server_config = config['mcpServers'][mcp_name]
                    status['configured'] = True
                    status['priority'] = server_config.get('settings', {}).get('priority', 'unknown')
                    status['timeout'] = server_config.get('settings', {}).get('timeout', 0)
                    status['copilot_enabled'] = server_config.get('settings', {}).get('copilotEnabled', False)
                    
                    # Define expected functionality
                    if mcp_name == 'bybit-trading':
                        status['critical_for_trading'] = True
                        status['expected_functionality'] = [
                            'Live market data',
                            'Order execution',
                            'Position monitoring',
                            'Risk assessment',
                            'Performance tracking'
                        ]
                    elif mcp_name == 'sequentialthinking':
                        status['expected_functionality'] = [
                            'Advanced reasoning',
                            'Problem decomposition',
                            'Strategy optimization',
                            'Pattern recognition'
                        ]
                    elif mcp_name == 'pylance':
                        status['expected_functionality'] = [
                            'Python code analysis',
                            'IntelliSense',
                            'Error detection',
                            'Import resolution'
                        ]
                    elif mcp_name == 'memory':
                        status['expected_functionality'] = [
                            'Persistent storage',
                            'Learning patterns',
                            'Context retention',
                            'Experience replay'
                        ]
                    elif mcp_name == 'postgres':
                        status['expected_functionality'] = [
                            'Database queries',
                            'Data persistence',
                            'Transaction management',
                            'Schema operations'
                        ]
                    elif mcp_name == 'mssql':
                        status['expected_functionality'] = [
                            'SQL Server operations',
                            'Data analysis',
                            'Reporting queries',
                            'Performance metrics'
                        ]
                    
                    logger.info(f"   ✅ {mcp_name}: Configured (Priority: {status['priority']})")
                else:
                    logger.warning(f"   ❌ {mcp_name}: Not configured")
                    
            except Exception as e:
                logger.error(f"   ❌ {mcp_name}: Error checking configuration - {e}")
                
            server_status[mcp_name] = status
            
        return server_status
    
    async def _verify_copilot_integration(self) -> Dict[str, Any]:
        """Verify Copilot integration settings"""
        logger.info("VERIFYING Copilot integration...")
        
        copilot_status = {
            'integration_configured': False,
            'context_sharing_enabled': False,
            'priority_servers_set': False,
            'fast_response_mode': False,
            'trading_optimized': False,
            'max_context_tokens': 0,
            'priority_servers': []
        }
        
        try:
            with open(self.mcp_config_path, 'r') as f:
                config = json.load(f)
                
            if 'copilotIntegration' in config:
                copilot_config = config['copilotIntegration']
                copilot_status['integration_configured'] = True
                copilot_status['context_sharing_enabled'] = copilot_config.get('enableContextSharing', False)
                copilot_status['fast_response_mode'] = copilot_config.get('fastResponseMode', False)
                copilot_status['trading_optimized'] = copilot_config.get('tradingOptimized', False)
                copilot_status['max_context_tokens'] = copilot_config.get('maxContextTokens', 0)
                copilot_status['priority_servers'] = copilot_config.get('priorityServers', [])
                copilot_status['priority_servers_set'] = len(copilot_status['priority_servers']) > 0
                
                # Check if bybit-trading is prioritized
                if 'bybit-trading' in copilot_status['priority_servers']:
                    logger.info("   ✅ Bybit trading MCP is prioritized for Copilot")
                else:
                    logger.warning("   ⚠️ Bybit trading MCP not in priority servers list")
                    
                logger.info(f"   ✅ Copilot integration configured with {len(copilot_status['priority_servers'])} priority servers")
            else:
                logger.warning("   ❌ Copilot integration section not found in configuration")
                
        except Exception as e:
            logger.error(f"   ❌ Error verifying Copilot integration: {e}")
            
        return copilot_status
    
    async def _verify_live_data_access(self) -> Dict[str, Any]:
        """Verify live data access and no fallbacks/shortcuts"""
        logger.info("📊 Verifying live data access (no fallbacks/shortcuts)...")
        
        live_data_status = {
            'bybit_api_configured': False,
            'database_connections': {},
            'real_time_data': False,
            'no_mock_data': True,
            'environment_variables': {}
        }
        
        try:
            with open(self.mcp_config_path, 'r') as f:
                config = json.load(f)
                
            # Check Bybit API configuration
            if 'bybit-trading' in config.get('mcpServers', {}):
                bybit_config = config['mcpServers']['bybit-trading']
                env_vars = bybit_config.get('env', {})
                
                live_data_status['environment_variables']['bybit_api_key'] = 'BYBIT_API_KEY' in env_vars
                live_data_status['environment_variables']['bybit_api_secret'] = 'BYBIT_API_SECRET' in env_vars
                live_data_status['environment_variables']['trading_mode'] = env_vars.get('TRADING_MODE') == 'live'
                
                live_data_status['bybit_api_configured'] = all([
                    live_data_status['environment_variables']['bybit_api_key'],
                    live_data_status['environment_variables']['bybit_api_secret']
                ])
                
                if live_data_status['bybit_api_configured']:
                    logger.info("   ✅ Bybit API properly configured for live data")
                else:
                    logger.warning("   ⚠️ Bybit API configuration incomplete")
            
            # Check database connections
            for db_mcp in ['postgres', 'mssql']:
                if db_mcp in config.get('mcpServers', {}):
                    db_config = config['mcpServers'][db_mcp]
                    connection_configured = bool(db_config.get('env', {}).get(f'{db_mcp.upper()}_CONNECTION_STRING'))
                    live_data_status['database_connections'][db_mcp] = connection_configured
                    
                    if connection_configured:
                        logger.info(f"   ✅ {db_mcp.upper()} connection configured")
                    else:
                        logger.warning(f"   ⚠️ {db_mcp.upper()} connection not configured")
            
            live_data_status['real_time_data'] = live_data_status['bybit_api_configured']
            
        except Exception as e:
            logger.error(f"   ❌ Error verifying live data access: {e}")
            
        return live_data_status
    
    async def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on verification results"""
        recommendations = []
        
        # Check configuration issues
        if not results['config_verification'].get('file_exists', False):
            recommendations.append("CRITICAL: Create MCP configuration file")
        
        # Check missing MCPs
        configured_mcps = results['mcp_status']
        for mcp in self.required_mcps:
            if not configured_mcps.get(mcp, {}).get('configured', False):
                recommendations.append(f"HIGH: Configure {mcp} MCP server")
        
        # Check Bybit trading priority
        if 'bybit-trading' not in results['copilot_integration'].get('priority_servers', []):
            recommendations.append("HIGH: Add bybit-trading to Copilot priority servers")
        
        # Check live data configuration
        if not results['live_data_verification'].get('bybit_api_configured', False):
            recommendations.append("CRITICAL: Configure Bybit API credentials for live trading")
        
        # Check Copilot optimization
        if not results['copilot_integration'].get('fast_response_mode', False):
            recommendations.append("MEDIUM: Enable fast response mode for Copilot")
        
        if not results['copilot_integration'].get('trading_optimized', False):
            recommendations.append("MEDIUM: Enable trading optimization for Copilot")
        
        return recommendations
    
    def _determine_overall_status(self, results: Dict[str, Any]) -> str:
        """Determine overall system status"""
        critical_issues = len([r for r in results.get('recommendations', []) if r.startswith('CRITICAL')])
        high_issues = len([r for r in results.get('recommendations', []) if r.startswith('HIGH')])
        
        if critical_issues > 0:
            return 'critical_issues'
        elif high_issues > 0:
            return 'minor_issues'
        else:
            return 'optimal'
    
    async def print_verification_report(self) -> None:
        """Print comprehensive verification report"""
        results = await self.verify_complete_system()
        
        print("\n" + "="*80)
        print("MCP SYSTEM VERIFICATION REPORT")
        print("="*80)
        print(f"Verification Time: {results['timestamp']}")
        print(f"Overall Status: {results['overall_status'].upper()}")
        print("="*80)
        
        print("\n📋 CONFIGURATION STATUS:")
        config = results['config_verification']
        print(f"   Config File Exists: {'✅' if config.get('file_exists') else '❌'}")
        print(f"   Valid JSON: {'✅' if config.get('valid_json') else '❌'}")
        print(f"   Servers Configured: {config.get('server_count', 0)}")
        print(f"   Critical MCPs: {len(config.get('critical_mcps_configured', []))}/{len(self.required_mcps)}")
        
        print("\n🔧 MCP SERVER STATUS:")
        for mcp_name, status in results['mcp_status'].items():
            status_icon = "✅" if status.get('configured') else "❌"
            priority = status.get('priority', 'unknown')
            copilot = "✅" if status.get('copilot_enabled') else "❌"
            print(f"   {status_icon} {mcp_name:<20} Priority: {priority:<8} Copilot: {copilot}")
        
        print("\nCOPILOT INTEGRATION:")
        copilot = results['copilot_integration']
        print(f"   Integration Configured: {'✅' if copilot.get('integration_configured') else '❌'}")
        print(f"   Context Sharing: {'✅' if copilot.get('context_sharing_enabled') else '❌'}")
        print(f"   Fast Response Mode: {'✅' if copilot.get('fast_response_mode') else '❌'}")
        print(f"   Trading Optimized: {'✅' if copilot.get('trading_optimized') else '❌'}")
        print(f"   Priority Servers: {', '.join(copilot.get('priority_servers', []))}")
        
        print("\n📊 LIVE DATA ACCESS:")
        live_data = results['live_data_verification']
        print(f"   Bybit API Configured: {'✅' if live_data.get('bybit_api_configured') else '❌'}")
        print(f"   Real-time Data: {'✅' if live_data.get('real_time_data') else '❌'}")
        print(f"   No Mock Data: {'✅' if live_data.get('no_mock_data') else '❌'}")
        
        db_connections = live_data.get('database_connections', {})
        for db, configured in db_connections.items():
            print(f"   {db.upper()} Connection: {'✅' if configured else '❌'}")
        
        print("\n📝 RECOMMENDATIONS:")
        recommendations = results.get('recommendations', [])
        if recommendations:
            for rec in recommendations:
                priority = rec.split(':')[0]
                message = ':'.join(rec.split(':')[1:])
                icon = "🔴" if priority == "CRITICAL" else "🟡" if priority == "HIGH" else "🔵"
                print(f"   {icon} {priority}: {message}")
        else:
            print("   ✅ No recommendations - system is optimally configured!")
        
        print("\n" + "="*80)
        print("VERIFICATION COMPLETE")
        print("="*80)

async def main():
    """Main verification entry point"""
    verifier = MCPSystemVerification()
    await verifier.print_verification_report()

if __name__ == "__main__":
    asyncio.run(main())
