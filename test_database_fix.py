#!/usr/bin/env python3
"""
Test Database Fix Script
Tests if the SQLite database compatibility issues have been resolved
"""

import os
import sys
import sqlite3
import asyncio
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_sqlite_database():
    """Test SQLite database operations"""
    try:
        db_path = "test_bybit_trading_bot.db"
        
        # Remove existing test database if it exists
        if os.path.exists(db_path):
            os.remove(db_path)
        
        # Create new SQLite database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test single statement execution (should work)
        logger.info("Testing single statement execution...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHA<PERSON>(100),
                value REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        logger.info("SUCCESS: Single statement execution works")
        
        # Test multi-statement execution (should fail with SQLite)
        logger.info("Testing multi-statement execution...")
        try:
            multi_statement_sql = """
            CREATE TABLE IF NOT EXISTS test_table2 (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data TEXT
            );
            
            CREATE TABLE IF NOT EXISTS test_table3 (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                info TEXT
            );
            """
            cursor.execute(multi_statement_sql)
            logger.warning("WARNING: Multi-statement execution unexpectedly succeeded")
        except sqlite3.ProgrammingError as e:
            if "You can only execute one statement at a time" in str(e):
                logger.info("EXPECTED: Multi-statement execution failed as expected")
            else:
                logger.error(f"UNEXPECTED ERROR: {e}")
        
        # Test individual statement execution (should work)
        logger.info("Testing individual statement execution...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table2 (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data TEXT
            )
        """)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_table3 (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                info TEXT
            )
        """)
        logger.info("SUCCESS: Individual statement execution works")
        
        # Test data insertion
        logger.info("Testing data insertion...")
        cursor.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", ("test", 123.45))
        cursor.execute("INSERT INTO test_table2 (data) VALUES (?)", ("test data",))
        cursor.execute("INSERT INTO test_table3 (info) VALUES (?)", ("test info",))
        
        # Test data retrieval
        logger.info("Testing data retrieval...")
        cursor.execute("SELECT COUNT(*) FROM test_table")
        count = cursor.fetchone()[0]
        logger.info(f"Test table has {count} records")
        
        conn.commit()
        conn.close()
        
        # Clean up test database
        os.remove(db_path)
        
        logger.info("SUCCESS: SQLite database test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"FAILED: SQLite database test failed: {e}")
        return False

async def test_database_manager():
    """Test if DatabaseManager can initialize without errors"""
    try:
        logger.info("Testing DatabaseManager initialization...")
        
        # Import the database manager
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from bybit_bot.database.connection import DatabaseManager
        from bybit_bot.config.enhanced_config import EnhancedBotConfig
        
        # Create config
        config = EnhancedBotConfig()
        
        # Create database manager
        db_manager = DatabaseManager(config)
        
        # Test initialization
        await db_manager.initialize()
        
        # Test basic operations
        await db_manager.execute_sql("SELECT 1")
        
        # Close connection
        await db_manager.close()
        
        logger.info("SUCCESS: DatabaseManager test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"FAILED: DatabaseManager test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("STARTING Database Fix Test...")
    
    # Test 1: Basic SQLite operations
    logger.info("Test 1: Basic SQLite operations")
    sqlite_test_passed = test_sqlite_database()
    
    # Test 2: DatabaseManager initialization
    logger.info("Test 2: DatabaseManager initialization")
    db_manager_test_passed = await test_database_manager()
    
    # Summary
    logger.info("=" * 50)
    logger.info("TEST RESULTS SUMMARY:")
    logger.info(f"SQLite Test: {'PASSED' if sqlite_test_passed else 'FAILED'}")
    logger.info(f"DatabaseManager Test: {'PASSED' if db_manager_test_passed else 'FAILED'}")
    
    if sqlite_test_passed and db_manager_test_passed:
        logger.info("SUCCESS: All database tests passed!")
        logger.info("The system should now be able to start without SQLite errors")
        return 0
    else:
        logger.error("FAILED: Some database tests failed")
        logger.error("The system may still have SQLite compatibility issues")
        return 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        sys.exit(1)
