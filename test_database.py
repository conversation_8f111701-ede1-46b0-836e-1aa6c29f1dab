#!/usr/bin/env python3
"""
Database diagnostic test script
"""
import sqlite3
import os
import sys

def test_database():
    """Test database accessibility and integrity"""
    db_path = 'data/bybit_trading_bot.db'
    
    print(f"Testing database: {db_path}")
    
    # Check if file exists
    if not os.path.exists(db_path):
        print("ERROR: Database file does not exist")
        return False
    
    # Check file size
    file_size = os.path.getsize(db_path)
    print(f"Database file size: {file_size} bytes")
    
    # Check file permissions
    readable = os.access(db_path, os.R_OK)
    writable = os.access(db_path, os.W_OK)
    print(f"File permissions - Readable: {readable}, Writable: {writable}")
    
    if not writable:
        print("ERROR: Database file is not writable")
        return False
    
    try:
        # Test database connection
        conn = sqlite3.connect(db_path)
        print("SUCCESS: Database connection established")
        
        # Test database integrity
        cursor = conn.cursor()
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()[0]
        print(f"Database integrity: {integrity_result}")
        
        if integrity_result != "ok":
            print("ERROR: Database integrity check failed")
            return False
        
        # List tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"Tables found: {tables}")
        
        # Test write operation
        cursor.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, test_data TEXT)")
        cursor.execute("INSERT INTO test_table (test_data) VALUES (?)", ("test_write_" + str(os.getpid()),))
        conn.commit()
        print("SUCCESS: Write test completed")
        
        # Test read operation
        cursor.execute("SELECT COUNT(*) FROM test_table")
        count = cursor.fetchone()[0]
        print(f"Test table records: {count}")
        
        # Clean up test
        cursor.execute("DELETE FROM test_table WHERE test_data LIKE 'test_write_%'")
        conn.commit()
        
        conn.close()
        print("SUCCESS: Database test completed successfully")
        return True
        
    except Exception as e:
        print(f"ERROR: Database test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_database()
    sys.exit(0 if success else 1)
