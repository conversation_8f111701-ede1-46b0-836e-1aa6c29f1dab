#!/usr/bin/env python3
"""
CRITICAL FIXES VERIFICATION SCRIPT
Tests the three major fixes: API signature, AI instantiation, Emergency warnings
"""

import sys
import os
import asyncio
import logging

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_ai_system_instantiation():
    """Test if AI system instantiation fix works"""
    print("=" * 60)
    print("TESTING AI SYSTEM INSTANTIATION FIX")
    print("=" * 60)
    
    try:
        from bybit_bot.ai.ai_folder_activation_manager import AIFolderActivationManager
        
        # Mock config
        config = type('MockConfig', (), {
            'ai': {'enabled': True},
            'database': {'path': 'bybit_trading_bot.db'},
            'api_keys': {'openrouter': {'api_key': 'mock'}},
            'trading': {'margin_trading_enabled': True}
        })()
        
        # Test instantiation
        ai_manager = AIFolderActivationManager(config, None, None)
        
        # Test intelligent_ml_system specifically
        try:
            from bybit_bot.ai.intelligent_ml_system import IntelligentMLSystem
            
            # Test class finding
            module = __import__('bybit_bot.ai.intelligent_ml_system', fromlist=[''])
            main_class = ai_manager._find_main_class(module, 'intelligent_ml_system')
            
            if main_class:
                print(f"SUCCESS: Found main class: {main_class.__name__}")
                
                # Test instantiation
                try:
                    instance = main_class({'ai': {'enabled': True}})
                    print(f"SUCCESS: Created instance of {main_class.__name__}")
                    return True
                except Exception as e:
                    print(f"ERROR: Failed to create instance: {e}")
                    return False
            else:
                print("ERROR: Could not find main class")
                return False
                
        except Exception as e:
            print(f"ERROR: Failed to import intelligent_ml_system: {e}")
            return False
            
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        return False

async def test_api_signature():
    """Test if API signature fix works"""
    print("\n" + "=" * 60)
    print("TESTING API SIGNATURE FIX")
    print("=" * 60)
    
    try:
        from bybit_bot.exchange.bybit_client import BybitClient
        from bybit_bot.core.config import BotConfig
        
        # Load config
        config = BotConfig()
        
        # Test signature creation
        client = BybitClient(config)
        
        # Test signature with empty params (should not include timestamp/recv_window)
        test_params = {"category": "linear", "symbol": "BTCUSDT"}
        timestamp = "1640995200000"  # Fixed timestamp for testing
        
        signature = client._create_signature("GET", test_params, timestamp)
        
        print(f"SUCCESS: Created signature: {signature[:10]}...")
        print(f"Test params: {test_params}")
        print(f"Timestamp: {timestamp}")
        
        # Verify the signing string doesn't contain timestamp/recv_window in params
        if "timestamp=" not in str(test_params) and "recv_window=" not in str(test_params):
            print("SUCCESS: Params don't contain timestamp/recv_window")
            return True
        else:
            print("ERROR: Params contain timestamp/recv_window")
            return False
            
    except Exception as e:
        print(f"ERROR: API signature test failed: {e}")
        return False

async def test_profit_enforcer_fix():
    """Test if profit enforcer emergency actions fix works"""
    print("\n" + "=" * 60)
    print("TESTING PROFIT ENFORCER FIX")
    print("=" * 60)
    
    try:
        from bybit_bot.profit_maximization.profit_target_enforcer import ProfitTargetEnforcer
        from bybit_bot.core.config import BotConfig
        
        config = BotConfig()
        enforcer = ProfitTargetEnforcer(config, None, None)
        
        # Test emergency actions with no trades executed
        await enforcer._emergency_actions()
        print("SUCCESS: Emergency actions handled correctly with no trades")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Profit enforcer test failed: {e}")
        return False

async def test_critical_fixes():
    """Run all critical fixes tests"""
    print("BYBIT TRADING BOT - CRITICAL FIXES VERIFICATION")
    print("=" * 60)
    
    # Test AI system
    ai_test_result = await test_ai_system_instantiation()
    
    # Test API signature
    api_test_result = await test_api_signature()
    
    # Test profit enforcer
    profit_test_result = await test_profit_enforcer_fix()
    
    # Results
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"AI System Instantiation: {'PASS' if ai_test_result else 'FAIL'}")
    print(f"API Signature Fix: {'PASS' if api_test_result else 'FAIL'}")
    print(f"Profit Enforcer Fix: {'PASS' if profit_test_result else 'FAIL'}")
    
    if ai_test_result and api_test_result and profit_test_result:
        print("\nSUCCESS: All critical fixes are working!")
        print("The bot should now start without these errors.")
    else:
        print("\nFAILURE: Some fixes need more work.")
    
    return ai_test_result and api_test_result and profit_test_result

if __name__ == "__main__":
    asyncio.run(test_critical_fixes())

if __name__ == "__main__":
    asyncio.run(test_critical_fixes())
