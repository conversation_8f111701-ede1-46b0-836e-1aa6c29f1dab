#!/usr/bin/env python3
"""
Test Trading Engines Startup
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("=== TESTING TRADING ENGINES STARTUP ===")
print(f"Starting at: {datetime.now()}")
print("This will test if the trading engines can start properly")
print("=" * 50)

async def test_engines():
    """Test if trading engines can start"""
    try:
        print("1. Importing main system...")
        from main import BybitTradingBotSystem
        
        print("2. Creating bot instance...")
        bot = BybitTradingBotSystem()
        
        print("3. Testing basic initialization...")
        # Initialize only essential components
        bot.is_running = True
        
        print("4. Testing individual engine methods...")
        
        # Test if we can create tasks for each engine
        print("   Creating profit enforcer task...")
        profit_task = asyncio.create_task(bot._run_profit_target_enforcer())
        
        print("   Creating meta-cognition task...")
        meta_task = asyncio.create_task(bot._run_meta_cognition())
        
        print("   Creating performance monitor task...")
        monitor_task = asyncio.create_task(bot._run_performance_monitor())
        
        print("   Creating risk manager task...")
        risk_task = asyncio.create_task(bot._run_risk_manager())
        
        print("   Creating strategy manager task...")
        strategy_task = asyncio.create_task(bot._run_strategy_manager())
        
        print("   Creating market predictor task...")
        predictor_task = asyncio.create_task(bot._run_market_predictor())
        
        print("   Creating main trading loop task...")
        main_task = asyncio.create_task(bot._run_main_trading_loop())
        
        print("   Creating advanced profit engine task...")
        advanced_task = asyncio.create_task(bot._run_advanced_profit_engine())
        
        print("   Creating hyper profit engine task...")
        hyper_task = asyncio.create_task(bot._run_hyper_profit_engine())
        
        tasks = [profit_task, meta_task, monitor_task, risk_task, strategy_task, predictor_task, main_task, advanced_task, hyper_task]
        
        print(f"5. Created {len(tasks)} engine tasks successfully")
        print("6. Running engines for 30 seconds to test...")
        
        # Run for 30 seconds to test
        try:
            await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=30.0)
        except asyncio.TimeoutError:
            print("SUCCESS: Engines ran for 30 seconds without crashing")
            
        # Cancel all tasks
        print("7. Stopping all engines...")
        bot.is_running = False
        for task in tasks:
            if not task.done():
                task.cancel()
        
        # Wait for cancellation
        await asyncio.gather(*tasks, return_exceptions=True)
        
        print("SUCCESS: All engines started and stopped properly")
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    success = await test_engines()
    if success:
        print("\n=== ENGINE TEST SUCCESSFUL ===")
        print("Trading engines can start properly")
        print("The system is ready for live trading")
    else:
        print("\n=== ENGINE TEST FAILED ===")
        print("There is an issue with the trading engines")
        print("Check the error messages above")

if __name__ == "__main__":
    asyncio.run(main())
