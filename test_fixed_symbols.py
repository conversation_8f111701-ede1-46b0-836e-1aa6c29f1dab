#!/usr/bin/env python3
"""
Test Fixed Symbol Issues
"""

import asyncio
import json
from datetime import datetime

try:
    from bybit_bot.core.config import ConfigManager
    from bybit_bot.core.enhanced_bybit_client import EnhancedBybitClient
    print("SUCCESS: Imports loaded")
except Exception as e:
    print(f"IMPORT ERROR: {e}")
    exit(1)

async def main():
    print("=== TESTING FIXED SYMBOL VALIDATION ===")
    print(f"Time: {datetime.now()}")
    
    try:
        # Load config
        config_manager = ConfigManager()
        config = config_manager.get_config()
        print("SUCCESS: Config loaded")
        
        # Check testnet setting
        testnet = config['bybit'].get('testnet', False)
        print(f"Testnet mode: {testnet}")
        
        # Create client
        client = EnhancedBybitClient(
            api_key=config['bybit']['api_key'],
            api_secret=config['bybit']['api_secret'],
            testnet=testnet
        )
        print("SUCCESS: Client created")
        
        # Test 1: Get spot instruments
        print("\n=== TEST 1: SPOT INSTRUMENTS ===")
        try:
            spot_instruments = await client.get_instruments_info(category="spot")
            if spot_instruments.get('retCode') == 0:
                symbols = [item['symbol'] for item in spot_instruments['result']['list'][:10]]
                print(f"SUCCESS: Found {len(spot_instruments['result']['list'])} spot symbols")
                print(f"First 10: {symbols}")
            else:
                print(f"FAILED: {spot_instruments}")
        except Exception as e:
            print(f"ERROR: {e}")
        
        # Test 2: Test BTCUSDT spot klines
        print("\n=== TEST 2: BTCUSDT SPOT KLINES ===")
        try:
            klines = await client.get_klines(
                category="spot",
                symbol="BTCUSDT",
                interval="1",
                limit=5
            )
            if klines.get('retCode') == 0:
                print("SUCCESS: BTCUSDT spot klines working")
                if klines['result']['list']:
                    latest = klines['result']['list'][0]
                    print(f"Latest price: {latest[4]}")
            else:
                print(f"FAILED: {klines}")
        except Exception as e:
            print(f"ERROR: {e}")
        
        # Test 3: Test ETHUSDT spot klines
        print("\n=== TEST 3: ETHUSDT SPOT KLINES ===")
        try:
            klines = await client.get_klines(
                category="spot",
                symbol="ETHUSDT",
                interval="1",
                limit=5
            )
            if klines.get('retCode') == 0:
                print("SUCCESS: ETHUSDT spot klines working")
                if klines['result']['list']:
                    latest = klines['result']['list'][0]
                    print(f"Latest price: {latest[4]}")
            else:
                print(f"FAILED: {klines}")
        except Exception as e:
            print(f"ERROR: {e}")
        
        # Test 4: Test market data method
        print("\n=== TEST 4: MARKET DATA METHOD ===")
        try:
            market_data = await client.get_market_data("BTCUSDT", "1", 5)
            if market_data:
                print(f"SUCCESS: Market data method working - got {len(market_data)} candles")
                if market_data:
                    print(f"Latest close: {market_data[0]['close']}")
            else:
                print("FAILED: No market data returned")
        except Exception as e:
            print(f"ERROR: {e}")
        
        # Test 5: Test account balance
        print("\n=== TEST 5: ACCOUNT BALANCE ===")
        try:
            balance = await client.get_wallet_balance(accountType="UNIFIED")
            if balance.get('retCode') == 0:
                print("SUCCESS: Account balance accessible")
                for account in balance['result']['list']:
                    for coin in account.get('coin', []):
                        if float(coin.get('walletBalance', 0)) > 0:
                            print(f"  {coin['coin']}: {coin['walletBalance']}")
            else:
                print(f"FAILED: {balance}")
        except Exception as e:
            print(f"ERROR: {e}")
        
        await client.close()
        print("\n=== SYMBOL FIX TEST COMPLETED ===")
        
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
