#!/usr/bin/env python3
"""
Quick system monitor to check if the trading bot is working
"""
import os
import time
import glob
from datetime import datetime

def monitor_system():
    """Monitor the system for activity"""
    print("=" * 60)
    print("QUICK SYSTEM MONITOR")
    print("=" * 60)
    
    # Check for Python processes
    print("\n1. Checking Python processes...")
    try:
        import subprocess
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and 'python.exe' in result.stdout:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # Header + at least one process
                print(f"   SUCCESS: Found {len(lines)-1} Python process(es) running")
                for line in lines[1:]:  # Skip header
                    parts = line.split(',')
                    if len(parts) >= 2:
                        name = parts[0].strip('"')
                        pid = parts[1].strip('"')
                        print(f"   - {name} (PID: {pid})")
            else:
                print("   WARNING: No Python processes found")
        else:
            print("   ERROR: Could not check Python processes")
    except Exception as e:
        print(f"   ERROR: Process check failed: {e}")
    
    # Check log files for recent activity
    print("\n2. Checking log files for recent activity...")
    log_patterns = [
        'logs/*.log',
        'logs/system/*.log',
        'logs/trading/*.log'
    ]
    
    recent_files = []
    current_time = time.time()
    
    for pattern in log_patterns:
        try:
            files = glob.glob(pattern)
            for file_path in files:
                if os.path.exists(file_path):
                    mtime = os.path.getmtime(file_path)
                    age_minutes = (current_time - mtime) / 60
                    if age_minutes < 5:  # Modified in last 5 minutes
                        size = os.path.getsize(file_path)
                        recent_files.append((file_path, age_minutes, size))
        except Exception as e:
            print(f"   WARNING: Error checking {pattern}: {e}")
    
    if recent_files:
        print(f"   SUCCESS: Found {len(recent_files)} recently active log files:")
        for file_path, age, size in sorted(recent_files, key=lambda x: x[1]):
            print(f"   - {file_path} (age: {age:.1f}min, size: {size} bytes)")
    else:
        print("   WARNING: No recently active log files found")
    
    # Check for specific error patterns in recent logs
    print("\n3. Checking for critical errors...")
    error_patterns = [
        "MetaCognitionEngine.*analyze_decision_context",
        "could not convert string to float.*'o'",
        "CRITICAL",
        "FAILED",
        "ERROR"
    ]
    
    errors_found = []
    for file_path, age, size in recent_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                for pattern in error_patterns:
                    if pattern.lower() in content.lower():
                        errors_found.append((file_path, pattern))
        except Exception as e:
            print(f"   WARNING: Could not read {file_path}: {e}")
    
    if errors_found:
        print(f"   WARNING: Found {len(errors_found)} error patterns:")
        for file_path, pattern in errors_found[:5]:  # Show first 5
            print(f"   - {pattern} in {file_path}")
    else:
        print("   SUCCESS: No critical error patterns found in recent logs")
    
    # Check for trading activity indicators
    print("\n4. Checking for trading activity...")
    trading_indicators = [
        "INTELLIGENT ML:",
        "ENSEMBLE DECISION:",
        "PROFIT TARGET",
        "Generated.*signals",
        "BTCUSDT.*Current:",
        "ETHUSDT.*Current:"
    ]
    
    activity_found = []
    for file_path, age, size in recent_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                recent_lines = lines[-50:] if len(lines) > 50 else lines  # Last 50 lines
                for line in recent_lines:
                    for indicator in trading_indicators:
                        if indicator.lower() in line.lower():
                            activity_found.append((indicator, line.strip()[:100]))
                            break
        except Exception as e:
            continue
    
    if activity_found:
        print(f"   SUCCESS: Found {len(activity_found)} trading activity indicators:")
        for indicator, line in activity_found[-5:]:  # Show last 5
            print(f"   - {indicator}: {line}")
    else:
        print("   WARNING: No trading activity indicators found")
    
    print("\n" + "=" * 60)
    print("SYSTEM MONITOR COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    monitor_system()
