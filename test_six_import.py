#!/usr/bin/env python3
"""
Test script to isolate the six import issue
"""
import sys
import traceback

def test_import(module_name, description):
    """Test importing a specific module"""
    print(f"\nTesting {description}...")
    try:
        if module_name == "hyper_profit_engine":
            from bybit_bot.profit_maximization.hyper_profit_engine import HyperProfitEngine
            print(f"SUCCESS: {description} imported successfully")
            return True
        elif module_name == "advanced_profit_engine":
            from bybit_bot.profit_maximization.advanced_profit_engine import AdvancedProfitEngine
            print(f"SUCCESS: {description} imported successfully")
            return True
        elif module_name == "enhanced_bybit_client":
            from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
            print(f"SUCCESS: {description} imported successfully")
            return True
        elif module_name == "bybit_client":
            from bybit_bot.exchange.bybit_client import BybitClient
            print(f"SUCCESS: {description} imported successfully")
            return True
        elif module_name == "config":
            from bybit_bot.core.config import BotConfig
            print(f"SUCCESS: {description} imported successfully")
            return True
        elif module_name == "database":
            from bybit_bot.database.connection import DatabaseManager
            print(f"SUCCESS: {description} imported successfully")
            return True
        else:
            print(f"ERROR: Unknown module {module_name}")
            return False
    except ImportError as e:
        print(f"IMPORT ERROR in {description}: {e}")
        if "six" in str(e):
            print(f"*** SIX DEPENDENCY ISSUE FOUND IN {description} ***")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"OTHER ERROR in {description}: {e}")
        traceback.print_exc()
        return False

def main():
    print("SIX DEPENDENCY ISOLATION TEST")
    print("=" * 50)
    
    # Test six directly first
    print("\nTesting six module directly...")
    try:
        import six
        print(f"SUCCESS: six is available (version {six.__version__})")
    except ImportError as e:
        print(f"ERROR: six not available: {e}")
        return
    
    # Test core modules first
    test_import("config", "BotConfig")
    test_import("database", "DatabaseManager")
    test_import("bybit_client", "Base BybitClient")
    test_import("enhanced_bybit_client", "Enhanced BybitClient")
    
    # Test profit engines (these are likely causing the issue)
    test_import("hyper_profit_engine", "Hyper Profit Engine")
    test_import("advanced_profit_engine", "Advanced Profit Engine")
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
