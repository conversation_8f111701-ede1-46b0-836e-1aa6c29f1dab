#!/usr/bin/env python3
"""
Fix pandas installation issue
"""
import subprocess
import sys
import os

def main():
    print("FIXING PANDAS INSTALLATION...")
    
    try:
        # Uninstall current pandas
        print("Uninstalling current pandas...")
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "pandas", "-y"], 
                      check=True, capture_output=True, text=True)
        print("SUCCESS: pandas uninstalled")
        
        # Clear pip cache
        print("Clearing pip cache...")
        subprocess.run([sys.executable, "-m", "pip", "cache", "purge"], 
                      check=True, capture_output=True, text=True)
        print("SUCCESS: pip cache cleared")
        
        # Install specific pandas version
        print("Installing pandas 2.0.3...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pandas==2.0.3", "--no-cache-dir"], 
                      check=True, capture_output=True, text=True)
        print("SUCCESS: pandas 2.0.3 installed")
        
        # Test import
        print("Testing pandas import...")
        import pandas as pd
        print(f"SUCCESS: pandas {pd.__version__} imported successfully")
        
        # Test basic functionality
        df = pd.DataFrame({'test': [1, 2, 3]})
        print(f"SUCCESS: pandas DataFrame created: {len(df)} rows")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Command failed: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False
    except ImportError as e:
        print(f"ERROR: Import failed: {e}")
        return False
    except Exception as e:
        print(f"ERROR: Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("PANDAS FIX COMPLETED SUCCESSFULLY")
    else:
        print("PANDAS FIX FAILED")
    sys.exit(0 if success else 1)
