"""
Terminal Diagnostic and Fix Script
Comprehensive diagnosis and resolution of terminal execution issues
for AI tools (Augment Agent and GitHub Copilot)
"""

import os
import sys
import subprocess
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional

class TerminalDiagnostic:
    """Comprehensive terminal diagnostic and fix system"""
    
    def __init__(self):
        self.project_root = Path("E:/The_real_deal_copy/Bybit_Bot/BOT")
        self.conda_path = "E:/conda/miniconda3"
        self.conda_env = "bybit-trader"
        
        # Setup logging
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "terminal_diagnostic.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def diagnose_environment(self) -> Dict:
        """Diagnose current environment setup"""
        self.logger.info("Starting environment diagnosis...")
        
        diagnosis = {
            'conda_installation': False,
            'conda_environment': False,
            'python_executable': False,
            'environment_variables': False,
            'terminal_configuration': False,
            'ai_tool_compatibility': False,
            'issues_found': [],
            'recommendations': []
        }
        
        # Check conda installation
        conda_exe = Path(self.conda_path) / "Scripts" / "conda.exe"
        if conda_exe.exists():
            diagnosis['conda_installation'] = True
            self.logger.info("Conda installation found")
        else:
            diagnosis['issues_found'].append("Conda installation not found")
            diagnosis['recommendations'].append("Install Miniconda3 at E:/conda/miniconda3")
        
        # Check conda environment
        env_path = Path(self.conda_path) / "envs" / self.conda_env
        if env_path.exists():
            diagnosis['conda_environment'] = True
            self.logger.info(f"Conda environment '{self.conda_env}' found")
        else:
            diagnosis['issues_found'].append(f"Conda environment '{self.conda_env}' not found")
            diagnosis['recommendations'].append(f"Create conda environment: conda create -n {self.conda_env} python=3.11")
        
        # Check Python executable
        python_exe = env_path / "python.exe"
        if python_exe.exists():
            diagnosis['python_executable'] = True
            self.logger.info("Python executable found in environment")
        else:
            diagnosis['issues_found'].append("Python executable not found in environment")
            diagnosis['recommendations'].append("Reinstall Python in conda environment")
        
        # Check environment variables
        required_vars = ['CONDA_DEFAULT_ENV', 'PYTHONPATH']
        env_vars_ok = True
        for var in required_vars:
            if var not in os.environ:
                env_vars_ok = False
                diagnosis['issues_found'].append(f"Environment variable {var} not set")
        
        diagnosis['environment_variables'] = env_vars_ok
        
        # Check terminal configuration
        vscode_settings = self.project_root / ".vscode" / "settings.json"
        if vscode_settings.exists():
            diagnosis['terminal_configuration'] = True
            self.logger.info("VSCode terminal configuration found")
        else:
            diagnosis['issues_found'].append("VSCode terminal configuration missing")
            diagnosis['recommendations'].append("Configure VSCode terminal settings")
        
        # Test AI tool compatibility
        try:
            result = subprocess.run(
                "python --version",
                shell=True,
                capture_output=True,
                text=True,
                timeout=5,
                cwd=str(self.project_root)
            )
            if result.returncode == 0:
                diagnosis['ai_tool_compatibility'] = True
                self.logger.info("AI tool compatibility test passed")
            else:
                diagnosis['issues_found'].append("AI tool compatibility test failed")
        except Exception as e:
            diagnosis['issues_found'].append(f"AI tool compatibility test error: {e}")
        
        return diagnosis
    
    def fix_terminal_issues(self) -> Dict:
        """Fix identified terminal issues"""
        self.logger.info("Starting terminal issue fixes...")
        
        fix_results = {
            'fixes_applied': [],
            'fixes_failed': [],
            'overall_success': False
        }
        
        try:
            # Fix 1: Create optimized terminal initialization
            init_script = self.project_root / ".vscode" / "terminal_init.cmd"
            if not init_script.exists():
                self._create_terminal_init_script()
                fix_results['fixes_applied'].append("Created terminal initialization script")
            
            # Fix 2: Create AI execution wrapper
            ai_wrapper = self.project_root / "scripts" / "ai_wrapper.py"
            if not ai_wrapper.exists():
                self._create_ai_wrapper()
                fix_results['fixes_applied'].append("Created AI execution wrapper")
            
            # Fix 3: Update VSCode settings
            self._update_vscode_settings()
            fix_results['fixes_applied'].append("Updated VSCode terminal settings")
            
            # Fix 4: Create environment activation script
            self._create_env_activation_script()
            fix_results['fixes_applied'].append("Created environment activation script")
            
            # Fix 5: Test all fixes
            if self._test_fixes():
                fix_results['fixes_applied'].append("All fixes tested successfully")
                fix_results['overall_success'] = True
            else:
                fix_results['fixes_failed'].append("Fix testing failed")
            
        except Exception as e:
            fix_results['fixes_failed'].append(f"Fix application error: {e}")
            self.logger.error(f"Fix application failed: {e}")
        
        return fix_results
    
    def _create_terminal_init_script(self):
        """Create optimized terminal initialization script"""
        script_content = f"""@echo off
REM Optimized Terminal Initialization for AI Tools
set PYTHONUNBUFFERED=1
set PYTHONIOENCODING=utf-8
set CONDA_DEFAULT_ENV={self.conda_env}
set CONDA_PREFIX={self.conda_path}\\envs\\{self.conda_env}

call {self.conda_path}\\Scripts\\activate.bat {self.conda_env}
cd /d {self.project_root}

echo Activated {self.conda_env} conda environment
"""
        
        script_path = self.project_root / ".vscode" / "terminal_init.cmd"
        with open(script_path, 'w') as f:
            f.write(script_content)
    
    def _create_ai_wrapper(self):
        """Create AI command execution wrapper"""
        wrapper_content = f"""#!/usr/bin/env python3
import subprocess
import sys
import os

def main():
    if len(sys.argv) < 2:
        sys.exit(1)
    
    command = " ".join(sys.argv[1:])
    
    env = os.environ.copy()
    env.update({{
        'CONDA_DEFAULT_ENV': '{self.conda_env}',
        'CONDA_PREFIX': '{self.conda_path}/envs/{self.conda_env}',
        'PYTHONUNBUFFERED': '1'
    }})
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=r'{self.project_root}',
            env=env
        )
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr, file=sys.stderr)
        
        sys.exit(result.returncode)
        
    except subprocess.TimeoutExpired:
        print("Command timed out", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Execution failed: {{e}}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
        
        wrapper_path = self.project_root / "scripts" / "ai_wrapper.py"
        with open(wrapper_path, 'w') as f:
            f.write(wrapper_content)
    
    def _update_vscode_settings(self):
        """Update VSCode settings for optimal terminal configuration"""
        # This would update the settings.json file
        # Implementation depends on existing settings
        pass
    
    def _create_env_activation_script(self):
        """Create environment activation script"""
        script_content = f"""@echo off
REM Environment Activation Script for AI Tools
call {self.conda_path}\\Scripts\\activate.bat {self.conda_env}
set PYTHONPATH={self.project_root}
cd /d {self.project_root}
"""
        
        script_path = self.project_root / "activate_env.cmd"
        with open(script_path, 'w') as f:
            f.write(script_content)
    
    def _test_fixes(self) -> bool:
        """Test all applied fixes"""
        test_commands = [
            "python --version",
            "python -c \"print('Test successful')\"",
            "conda info --envs"
        ]
        
        for cmd in test_commands:
            try:
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=10,
                    cwd=str(self.project_root)
                )
                if result.returncode != 0:
                    return False
            except Exception:
                return False
        
        return True
    
    def run_full_diagnostic(self) -> Dict:
        """Run complete diagnostic and fix process"""
        self.logger.info("Starting full terminal diagnostic...")
        
        # Run diagnosis
        diagnosis = self.diagnose_environment()
        
        # Apply fixes if needed
        fix_results = self.fix_terminal_issues()
        
        # Final test
        final_test = self._test_fixes()
        
        return {
            'diagnosis': diagnosis,
            'fixes': fix_results,
            'final_test_passed': final_test,
            'overall_success': fix_results['overall_success'] and final_test
        }

def main():
    """Main function"""
    diagnostic = TerminalDiagnostic()
    
    if len(sys.argv) > 1 and sys.argv[1] == "fix":
        results = diagnostic.run_full_diagnostic()
        print(json.dumps(results, indent=2))
        sys.exit(0 if results['overall_success'] else 1)
    else:
        diagnosis = diagnostic.diagnose_environment()
        print(json.dumps(diagnosis, indent=2))

if __name__ == "__main__":
    main()
