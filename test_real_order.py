#!/usr/bin/env python3
"""
Test Real Order Placement
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
from bybit_bot.core.config import BotConfig

def log_to_file(message):
    """Log message to file and print"""
    with open("test_real_order_output.txt", "a", encoding="utf-8") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        f.write(log_msg + "\n")
        f.flush()

async def test_real_order():
    """Test placing a real order on Bybit"""
    log_to_file("=== TESTING REAL ORDER PLACEMENT ===")
    log_to_file("WARNING: This will place a REAL order with REAL money!")
    log_to_file("Make sure you want to proceed...")
    log_to_file("=" * 50)
    
    try:
        # Load config
        config = BotConfig()
        
        # Initialize Bybit client
        log_to_file("1. Initializing Enhanced Bybit client...")
        client = EnhancedBybitClient(config)
        await client.initialize()
        log_to_file("SUCCESS: Client initialized")
        
        # Check account balance first
        log_to_file("2. Checking account balance...")
        balance_data = await client.get_account_balance()
        if balance_data:
            total_equity = balance_data.get('total_equity', 0)
            available_balance = balance_data.get('available_balance', 0)
            log_to_file(f"Total Equity: ${total_equity:.2f}")
            log_to_file(f"Available Balance: ${available_balance:.2f}")

            if total_equity < 1:
                log_to_file("ERROR: Insufficient balance for testing")
                return False
        else:
            log_to_file("ERROR: Could not retrieve account balance")
            return False
        
        # Get current market price for BTCUSDT
        log_to_file("3. Getting current market price...")
        market_data = await client.get_market_data("BTCUSDT", "1", 1)
        if not market_data:
            log_to_file("ERROR: Could not get market data")
            return False

        current_price = float(market_data[0]['close'])
        log_to_file(f"Current BTC price: ${current_price:.2f}")

        # Calculate minimum order size (very small for testing)
        min_order_usd = 1.0  # $1 minimum
        quantity = min_order_usd / current_price
        quantity = max(0.00001, round(quantity, 5))  # BTC precision

        log_to_file("4. Preparing test order...")
        log_to_file(f"Symbol: BTCUSDT")
        log_to_file(f"Side: Buy")
        log_to_file(f"Quantity: {quantity} BTC")
        log_to_file(f"Order Value: ${quantity * current_price:.2f}")
        log_to_file(f"Order Type: Market")
        log_to_file(f"Category: spot")
        
        # Ask for confirmation
        log_to_file("5. FINAL CONFIRMATION")
        log_to_file("This will place a REAL order with REAL money!")
        log_to_file("Type 'YES' to proceed or anything else to cancel:")

        # For automated testing, we'll skip the confirmation
        # In a real scenario, you'd want manual confirmation
        proceed = "YES"  # input().strip()

        if proceed != "YES":
            log_to_file("Order cancelled by user")
            return False

        log_to_file("6. Placing REAL order...")
        order_result = await client.place_order(
            symbol="BTCUSDT",
            side="Buy",
            quantity=quantity,
            order_type="Market",
            category="spot"
        )

        if order_result:
            log_to_file("SUCCESS: Order placed!")
            log_to_file(f"Order ID: {order_result}")
            log_to_file(f"Order details: Buy {quantity} BTC at market price")
            log_to_file(f"Estimated cost: ${quantity * current_price:.2f}")

            # Check updated balance
            log_to_file("7. Checking updated balance...")
            await asyncio.sleep(2)  # Wait for order to settle
            new_balance = await client.get_account_balance()
            if new_balance:
                new_equity = new_balance.get('total_equity', 0)
                log_to_file(f"New Total Equity: ${new_equity:.2f}")
                log_to_file(f"Balance change: ${new_equity - total_equity:.2f}")

            return True
        else:
            log_to_file("ERROR: Order placement failed")
            return False
            
    except Exception as e:
        log_to_file(f"ERROR: {e}")
        import traceback
        log_to_file(f"TRACEBACK: {traceback.format_exc()}")
        return False

async def main():
    """Main function"""
    # Clear previous output
    with open("test_real_order_output.txt", "w") as f:
        f.write("")

    success = await test_real_order()
    if success:
        log_to_file("=== TEST SUCCESSFUL ===")
        log_to_file("Real order placement is working!")
        log_to_file("The trading system can execute real trades")
    else:
        log_to_file("=== TEST FAILED ===")
        log_to_file("There is an issue with order placement")
        log_to_file("Check API permissions and account status")

if __name__ == "__main__":
    asyncio.run(main())
