#!/usr/bin/env python3
"""
Simple test of the rate limiter configuration
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def test_rate_limiter():
    """Test rate limiter basic functionality"""
    print("Testing rate limiter configuration...")
    
    try:
        # Import rate limiter
        from bybit_bot.utils.global_rate_limiter import rate_limiter
        
        print(f"Rate limiter configuration:")
        print(f"  - Requests per second: {rate_limiter.config.requests_per_second}")
        print(f"  - Requests per minute: {rate_limiter.config.requests_per_minute}")
        print(f"  - Burst limit: {rate_limiter.config.burst_limit}")
        print(f"  - Emergency delay: {rate_limiter.config.emergency_delay}")
        print(f"  - Max concurrent: {rate_limiter.config.max_concurrent}")
        print(f"  - Emergency mode: {rate_limiter.emergency_mode}")
        print(f"  - Consecutive errors: {rate_limiter.consecutive_errors}")
        
        # Test a few acquisitions
        print("\nTesting rate limiter acquisitions...")
        for i in range(5):
            start_time = time.time()
            await rate_limiter.acquire("normal")
            end_time = time.time()
            print(f"  Acquisition {i+1}: {end_time - start_time:.3f}s")
            rate_limiter.report_success()
        
        print(f"\nFinal state:")
        print(f"  - Emergency mode: {rate_limiter.emergency_mode}")
        print(f"  - Consecutive errors: {rate_limiter.consecutive_errors}")
        
        if not rate_limiter.emergency_mode:
            print("\nSUCCESS: Rate limiter is working correctly!")
            return True
        else:
            print("\nFAILURE: Rate limiter is in emergency mode")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_rate_limiter())
    sys.exit(0 if success else 1)
