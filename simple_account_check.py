#!/usr/bin/env python3
"""
Simple Account Check - Direct API verification
"""

import asyncio
import json
from datetime import datetime

# Direct import approach
try:
    from bybit_bot.core.config import ConfigManager
    from bybit_bot.core.enhanced_bybit_client import EnhancedBybitClient
    print("SUCCESS: Imports loaded")
except Exception as e:
    print(f"IMPORT ERROR: {e}")
    exit(1)

async def main():
    print("=== SIMPLE ACCOUNT CHECK ===")
    print(f"Time: {datetime.now()}")
    
    try:
        # Load config
        config_manager = ConfigManager()
        config = config_manager.get_config()
        print("SUCCESS: Config loaded")
        
        # Check testnet setting
        testnet = config['bybit'].get('testnet', False)
        print(f"Testnet mode: {testnet}")
        
        # Create client
        client = EnhancedBybitClient(
            api_key=config['bybit']['api_key'],
            api_secret=config['bybit']['api_secret'],
            testnet=testnet
        )
        print("SUCCESS: Client created")
        
        # Simple balance check
        print("\nChecking wallet balance...")
        balance = await client.get_wallet_balance(accountType="UNIFIED")
        print(f"Balance result: {json.dumps(balance, indent=2)}")
        
        # Check recent orders
        print("\nChecking recent orders...")
        orders = await client.get_order_history(category="spot", limit=5)
        print(f"Orders result: {json.dumps(orders, indent=2)}")
        
        await client.close()
        print("SUCCESS: Check completed")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
