#!/usr/bin/env python3
"""
Minimal test to check if the trading system can start
"""

import asyncio
import logging
import sys
from pathlib import Path
from dotenv import load_dotenv

print("MINIMAL TRADING SYSTEM TEST")
print("=" * 40)

# Setup
load_dotenv()
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
Path("logs").mkdir(exist_ok=True)

# Simple logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/minimal_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("MinimalTest")

async def test_core_components():
    """Test core components one by one"""
    try:
        print("Testing config...")
        from bybit_bot.core.config import BotConfig
        config = BotConfig()
        print("✓ Config loaded")
        
        print("Testing database...")
        from bybit_bot.database.connection import DatabaseManager
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        print("✓ Database initialized")
        
        print("Testing Bybit client...")
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        bybit_client = EnhancedBybitClient(config)
        await bybit_client.initialize()
        print("✓ Bybit client initialized")
        
        print("Testing account info...")
        account_info = await bybit_client.get_account_info()
        if account_info:
            print(f"✓ Account connected - Balance: {account_info.get('totalWalletBalance', 'N/A')}")
        else:
            print("⚠ Account info not available")
        
        print("Testing market data...")
        market_data = await bybit_client.get_klines("BTCUSDT", "1m", 5)
        if market_data:
            print(f"✓ Market data received - {len(market_data)} candles")
        else:
            print("⚠ Market data not available")
        
        print("\nCORE SYSTEM TEST COMPLETED SUCCESSFULLY!")
        print("The trading system should be able to start normally.")
        
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("Starting minimal system test...")
    success = await test_core_components()
    
    if success:
        print("\n" + "=" * 40)
        print("SYSTEM READY FOR FULL STARTUP")
        print("You can now run main.py")
        print("=" * 40)
    else:
        print("\n" + "=" * 40)
        print("SYSTEM HAS ISSUES - CHECK ERRORS ABOVE")
        print("=" * 40)

if __name__ == "__main__":
    asyncio.run(main())
