#!/usr/bin/env python3
"""
Basic Python test to check if the environment is working
"""

print("Testing basic Python functionality...")
print("Python is working correctly!")

# Test basic imports
try:
    import sys
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
except Exception as e:
    print(f"Error with sys import: {e}")

try:
    import os
    print(f"Current working directory: {os.getcwd()}")
except Exception as e:
    print(f"Error with os import: {e}")

print("Basic Python test completed successfully!")
