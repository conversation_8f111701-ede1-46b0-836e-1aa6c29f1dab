#!/usr/bin/env python3
"""
Quick test of rate limiter
"""

import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("Testing rate limiter...")

try:
    from bybit_bot.utils.global_rate_limiter import rate_limiter
    print(f"Rate limiter config: {rate_limiter.config.requests_per_second} req/sec")
    print(f"Emergency mode: {rate_limiter.emergency_mode}")
    print("SUCCESS: Rate limiter imported and configured correctly!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
