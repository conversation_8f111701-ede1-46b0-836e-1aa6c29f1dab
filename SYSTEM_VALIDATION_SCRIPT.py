#!/usr/bin/env python3
"""
SYSTEM VALIDATION SCRIPT
Run this after manual reconstruction to verify system functionality
"""
import sys
import os
import subprocess
from pathlib import Path
import json
from datetime import datetime
from typing import List, Any

def log_result(message: str, success: bool = True) -> bool:
    """Log validation results"""
    status = "SUCCESS" if success else "FAILED"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] [{status}] {message}\n"

    with open("validation_results.txt", "a", encoding="utf-8") as f:
        f.write(log_entry)

    print(f"[{status}] {message}")
    return success

def validate_python_environment() -> bool:
    """Validate Python environment"""
    print("\n=== PYTHON ENVIRONMENT VALIDATION ===")

    results: List[bool] = []
    
    # Check Python version
    try:
        version = sys.version_info
        if version.major == 3 and version.minor == 11:
            results.append(log_result(f"Python version: {sys.version}"))
        else:
            results.append(log_result(f"Wrong Python version: {sys.version}", False))
    except Exception as e:
        results.append(log_result(f"Python version check failed: {e}", False))
    
    # Check Python executable path
    try:
        exe_path = sys.executable
        if "bybit-trader" in exe_path and "E:\\conda\\Miniconda3" in exe_path:
            results.append(log_result(f"Python executable: {exe_path}"))
        else:
            results.append(log_result(f"Wrong Python executable: {exe_path}", False))
    except Exception as e:
        results.append(log_result(f"Python executable check failed: {e}", False))
    
    return all(results)

def validate_conda_environment() -> bool:
    """Validate conda environment"""
    print("\n=== CONDA ENVIRONMENT VALIDATION ===")

    results: List[bool] = []
    
    # Check conda installation
    conda_path = Path("E:\\conda\\Miniconda3\\Scripts\\conda.exe")
    if conda_path.exists():
        results.append(log_result(f"Conda executable found: {conda_path}"))
    else:
        results.append(log_result(f"Conda executable missing: {conda_path}", False))

    # Check environment directory
    env_path = Path("E:\\conda\\Miniconda3\\envs\\bybit-trader")
    if env_path.exists():
        results.append(log_result(f"Environment directory found: {env_path}"))
    else:
        results.append(log_result(f"Environment directory missing: {env_path}", False))
    
    # Check conda environment list
    try:
        result = subprocess.run([
            "E:\\conda\\Miniconda3\\Scripts\\conda.exe",
            "info", "--envs"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "bybit-trader" in result.stdout:
            results.append(log_result("Conda environment 'bybit-trader' found"))
        else:
            results.append(log_result("Conda environment 'bybit-trader' not found", False))
    except Exception as e:
        results.append(log_result(f"Conda environment check failed: {e}", False))
    
    return all(results)

def validate_dependencies() -> bool:
    """Validate installed dependencies"""
    print("\n=== DEPENDENCY VALIDATION ===")

    results: List[bool] = []
    
    # Core dependencies
    core_deps = [
        "aiohttp", "websockets", "pandas", "numpy",
        "redis", "dotenv", "fastapi", "uvicorn", "loguru", "yaml"
    ]
    
    for dep in core_deps:
        try:
            if dep == "dotenv":
                __import__("dotenv")
            elif dep == "yaml":
                __import__("yaml")
            else:
                __import__(dep)
            results.append(log_result(f"Core dependency '{dep}' imported successfully"))
        except ImportError as e:
            results.append(log_result(f"Core dependency '{dep}' import failed: {e}", False))
    
    # Trading dependencies
    trading_deps = ["ccxt", "requests", "pybit", "ta"]
    
    for dep in trading_deps:
        try:
            __import__(dep)
            results.append(log_result(f"Trading dependency '{dep}' imported successfully"))
        except ImportError as e:
            results.append(log_result(f"Trading dependency '{dep}' import failed: {e}", False))
    
    # AI dependencies (PyTorch-based system)
    ai_deps = ["torch", "sklearn", "transformers"]

    for dep in ai_deps:
        try:
            if dep == "sklearn":
                __import__("sklearn")
            else:
                __import__(dep)
            results.append(log_result(f"AI dependency '{dep}' imported successfully"))
        except ImportError as e:
            results.append(log_result(f"AI dependency '{dep}' import failed: {e}", False))
    
    return all(results)

def validate_trading_bot_modules() -> bool:
    """Validate trading bot module imports"""
    print("\n=== TRADING BOT MODULE VALIDATION ===")

    results: List[bool] = []
    
    # Test core config import
    try:
        from bybit_bot.core.config import EnhancedBotConfig
        results.append(log_result("EnhancedBotConfig import successful"))
    except ImportError as e:
        results.append(log_result(f"EnhancedBotConfig import failed: {e}", False))
    
    # Test database manager import
    try:
        from bybit_bot.database.connection import DatabaseManager
        results.append(log_result("DatabaseManager import successful"))
    except ImportError as e:
        results.append(log_result(f"DatabaseManager import failed: {e}", False))
    
    # Test Bybit client import
    try:
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        results.append(log_result("EnhancedBybitClient import successful"))
    except ImportError as e:
        results.append(log_result(f"EnhancedBybitClient import failed: {e}", False))
    
    return all(results)

def validate_environment_variables() -> bool:
    """Validate environment variables"""
    print("\n=== ENVIRONMENT VARIABLES VALIDATION ===")

    results: List[bool] = []
    
    # Check .env file
    env_file = Path(".env")
    if env_file.exists():
        results.append(log_result(".env file found"))
    else:
        results.append(log_result(".env file missing", False))
    
    # Check critical environment variables
    critical_vars = ["BYBIT_API_KEY", "BYBIT_API_SECRET"]
    
    for var in critical_vars:
        value = os.environ.get(var)
        if value:
            results.append(log_result(f"{var} is set (masked: {value[:8]}...)"))
        else:
            results.append(log_result(f"{var} is not set", False))
    
    return all(results)

def create_validation_summary() -> None:
    """Create validation summary"""
    print("\n=== VALIDATION SUMMARY ===")
    
    summary = {
        "timestamp": datetime.now().isoformat(),
        "python_version": sys.version,
        "python_executable": sys.executable,
        "working_directory": os.getcwd(),
        "validation_file": "validation_results.txt"
    }
    
    with open("validation_summary.json", "w") as f:
        json.dump(summary, f, indent=2)
    
    log_result("Validation summary saved to validation_summary.json")

def main() -> None:
    """Main validation function"""
    # Clear previous results
    if Path("validation_results.txt").exists():
        Path("validation_results.txt").unlink()
    
    print("SYSTEM VALIDATION STARTING")
    print("=" * 50)
    
    # Run all validations
    validations = [
        validate_python_environment(),
        validate_conda_environment(), 
        validate_dependencies(),
        validate_trading_bot_modules(),
        validate_environment_variables()
    ]
    
    # Create summary
    create_validation_summary()
    
    # Overall result
    success_count = sum(validations)
    total_count = len(validations)
    
    print("\n" + "=" * 50)
    print(f"VALIDATION COMPLETE: {success_count}/{total_count} categories passed")
    
    if success_count == total_count:
        print("SUCCESS: System is ready for trading operations")
        return True
    else:
        print("FAILED: System requires additional fixes")
        print("Check validation_results.txt for detailed error information")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
