@echo off
echo ========================================
echo RESTARTING SYSTEM AFTER DATABASE FIXES
echo ========================================
echo.

echo Step 1: Killing all Python processes...
taskkill /F /IM python.exe /T 2>nul
taskkill /F /IM pythonw.exe /T 2>nul
echo Python processes killed.
echo.

echo Step 2: Waiting for processes to terminate...
timeout /t 3 /nobreak >nul
echo.

echo Step 3: Removing problematic database file...
if exist "bybit_trading_bot.db" (
    del "bybit_trading_bot.db" 2>nul
    echo Removed existing SQLite database
) else (
    echo No existing SQLite database found
)
echo.

echo Step 4: Clearing Python cache...
if exist "__pycache__" (
    rmdir /s /q "__pycache__" 2>nul
    echo Cleared main Python cache
)
for /d %%i in (*) do (
    if exist "%%i\__pycache__" (
        rmdir /s /q "%%i\__pycache__" 2>nul
        echo Cleared %%i cache
    )
)
echo.

echo Step 5: Removing any lock files...
if exist "*.lock" del "*.lock" 2>nul
if exist "*.pid" del "*.pid" 2>nul
echo.

echo ========================================
echo DATABASE FIXES APPLIED:
echo ========================================
echo - Fixed database_init.py PostgreSQL syntax
echo - Fixed database_init_standalone.py multi-statement execution
echo - Fixed database_init_production.py multi-statement execution
echo - All files now use SQLite-compatible syntax
echo - Individual statement execution implemented
echo.

echo ========================================
echo SYSTEM READY TO RESTART
echo ========================================
echo.
echo The SQLite multi-statement execution errors should now be resolved.
echo You can now try running: python main.py
echo.
echo If the system still hangs, there may be additional issues to investigate.
echo.
pause
