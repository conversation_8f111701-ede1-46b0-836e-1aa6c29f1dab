#!/usr/bin/env python3
"""
SQLite Database Initialization for Production Trading
REAL MONEY TRADING DATABASE - NO SIMULATIONS
"""

import sqlite3
import os
import logging
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("PRODUCTION_SQLITE_INIT")

def create_production_sqlite_database():
    """Create production SQLite database for REAL TRADING"""
    try:
        logger.info("STARTING: INITIALIZING PRODUCTION SQLITE DATABASE FOR REAL TRADING")

        # Ensure data directory exists
        db_path = Path("E:/bybit_bot_data/bybit_trading_bot_production.db")
        db_path.parent.mkdir(parents=True, exist_ok=True)

        # Connect to SQLite database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        logger.info("SUCCESS: PRODUCTION SQLITE DATABASE CONNECTED")
        
        # Create production schema
        production_schema_sql = """
        -- PRODUCTION TRADING TABLES - REAL MONEY DATA ONLY
        
        -- Trading positions table
        CREATE TABLE IF NOT EXISTS trading_positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            size REAL NOT NULL,
            entry_price REAL NOT NULL,
            current_price REAL,
            unrealized_pnl REAL,
            realized_pnl REAL DEFAULT 0,
            leverage INTEGER DEFAULT 1,
            margin_used REAL,
            stop_loss REAL,
            take_profit REAL,
            status VARCHAR(20) DEFAULT 'open',
            opened_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            closed_at DATETIME,
            strategy VARCHAR(50),
            notes TEXT,
            is_production BOOLEAN DEFAULT TRUE
        );
        
        -- Trading orders table
        CREATE TABLE IF NOT EXISTS trading_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id VARCHAR(100) UNIQUE NOT NULL,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            order_type VARCHAR(20) NOT NULL,
            quantity REAL NOT NULL,
            price REAL,
            filled_quantity REAL DEFAULT 0,
            average_price REAL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            filled_at DATETIME,
            commission REAL DEFAULT 0,
            strategy VARCHAR(50),
            is_production BOOLEAN DEFAULT TRUE
        );
        
        -- Account balance table
        CREATE TABLE IF NOT EXISTS account_balance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            total_balance REAL NOT NULL,
            available_balance REAL NOT NULL,
            used_margin REAL DEFAULT 0,
            unrealized_pnl REAL DEFAULT 0,
            total_equity REAL NOT NULL,
            currency VARCHAR(10) DEFAULT 'USDT',
            is_production BOOLEAN DEFAULT TRUE
        );
        
        -- Market data table
        CREATE TABLE IF NOT EXISTS market_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(20) NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume REAL NOT NULL,
            quote_volume REAL,
            trades_count INTEGER,
            interval_type VARCHAR(10) DEFAULT '1m'
        );
        
        -- Trading signals table
        CREATE TABLE IF NOT EXISTS trading_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol VARCHAR(20) NOT NULL,
            signal_type VARCHAR(20) NOT NULL,
            direction VARCHAR(10) NOT NULL,
            strength REAL NOT NULL,
            price REAL NOT NULL,
            strategy VARCHAR(50) NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            executed BOOLEAN DEFAULT FALSE,
            execution_price REAL,
            execution_time DATETIME,
            profit_loss REAL,
            is_production BOOLEAN DEFAULT TRUE
        );
        
        -- Performance metrics table
        CREATE TABLE IF NOT EXISTS performance_metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            total_trades INTEGER DEFAULT 0,
            winning_trades INTEGER DEFAULT 0,
            losing_trades INTEGER DEFAULT 0,
            total_profit_loss REAL DEFAULT 0,
            win_rate REAL DEFAULT 0,
            average_profit REAL DEFAULT 0,
            average_loss REAL DEFAULT 0,
            max_drawdown REAL DEFAULT 0,
            sharpe_ratio REAL DEFAULT 0,
            profit_factor REAL DEFAULT 0,
            total_volume REAL DEFAULT 0,
            is_production BOOLEAN DEFAULT TRUE
        );

        -- Bot configuration table
        CREATE TABLE IF NOT EXISTS bot_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value TEXT,
            environment VARCHAR(20) DEFAULT 'PRODUCTION',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        );
        
        -- News and sentiment data table
        CREATE TABLE IF NOT EXISTS news_sentiment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            source VARCHAR(100) NOT NULL,
            title TEXT NOT NULL,
            content TEXT,
            sentiment_score REAL,
            impact_score REAL,
            symbols TEXT,
            url TEXT,
            published_at DATETIME
        );
        
        -- System logs table
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            level VARCHAR(20) NOT NULL,
            component VARCHAR(100) NOT NULL,
            message TEXT NOT NULL,
            details TEXT,
            is_production BOOLEAN DEFAULT TRUE
        );
        """
        
        cursor.executescript(production_schema_sql)
        logger.info("SUCCESS: PRODUCTION SCHEMA CREATED")

        # Create indexes for performance
        indexes_sql = """
        CREATE INDEX IF NOT EXISTS idx_positions_symbol ON trading_positions(symbol);
        CREATE INDEX IF NOT EXISTS idx_positions_status ON trading_positions(status);
        CREATE INDEX IF NOT EXISTS idx_positions_opened_at ON trading_positions(opened_at);
        CREATE INDEX IF NOT EXISTS idx_orders_symbol ON trading_orders(symbol);
        CREATE INDEX IF NOT EXISTS idx_orders_status ON trading_orders(status);
        CREATE INDEX IF NOT EXISTS idx_orders_created_at ON trading_orders(created_at);
        CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol);
        CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp);
        CREATE INDEX IF NOT EXISTS idx_signals_symbol ON trading_signals(symbol);
        CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON trading_signals(timestamp);
        CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_metrics(timestamp);
        CREATE INDEX IF NOT EXISTS idx_news_timestamp ON news_sentiment(timestamp);
        CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON system_logs(timestamp);
        """

        cursor.executescript(indexes_sql)
        logger.info("SUCCESS: PRODUCTION INDEXES CREATED")
        
        # Insert PRODUCTION configuration
        production_config_sql = """
        INSERT OR REPLACE INTO bot_config (config_key, config_value, environment) VALUES
        ('system_initialized', '{"value": true, "mode": "PRODUCTION", "timestamp": "' || datetime('now') || '"}', 'PRODUCTION'),
        ('trading_enabled', '{"value": true, "mode": "LIVE_TRADING"}', 'PRODUCTION'),
        ('real_money_trading', '{"value": true, "confirmed": true}', 'PRODUCTION'),
        ('simulation_mode', '{"value": false, "disabled": true}', 'PRODUCTION'),
        ('paper_trading', '{"value": false, "disabled": true}', 'PRODUCTION'),
        ('auto_healing_enabled', '{"value": true}', 'PRODUCTION'),
        ('risk_limits', '{"max_position_size": 0.50, "max_daily_loss": 0.15, "stop_loss": 0.03}', 'PRODUCTION'),
        ('api_environment', '{"value": "PRODUCTION", "testnet": false}', 'PRODUCTION'),
        ('profit_maximization', '{"value": true, "enabled": true}', 'PRODUCTION'),
        ('database_type', '{"value": "sqlite", "path": "E:/bybit_bot_data/bybit_trading_bot_production.db"}', 'PRODUCTION');
        """
        
        cursor.executescript(production_config_sql)
        logger.info("SUCCESS: PRODUCTION CONFIGURATION INSERTED")

        # Insert initial balance record
        initial_balance_sql = """
        INSERT INTO account_balance (total_balance, available_balance, total_equity, currency, is_production)
        VALUES (1000.0, 1000.0, 1000.0, 'USDT', TRUE);
        """

        cursor.execute(initial_balance_sql)
        logger.info("SUCCESS: INITIAL BALANCE RECORD CREATED")

        # Commit and close
        conn.commit()
        cursor.close()
        conn.close()

        logger.info("SUCCESS: PRODUCTION SQLITE DATABASE INITIALIZED FOR REAL TRADING!")
        logger.info("READY: READY FOR LIVE MONEY OPERATIONS!")
        logger.info("PRODUCTION: NO SIMULATIONS - REAL DATA ONLY!")
        logger.info(f"DATABASE: Database location: {db_path}")
        return True

    except Exception as e:
        logger.error(f"ERROR: PRODUCTION SQLITE DATABASE INITIALIZATION FAILED: {e}")
        return False

if __name__ == "__main__":
    logger.info("STARTING: STARTING PRODUCTION SQLITE DATABASE SETUP...")
    logger.info("PRODUCTION: REAL MONEY TRADING DATABASE")
    logger.info("PRODUCTION: NO SIMULATIONS ALLOWED")

    success = create_production_sqlite_database()

    if success:
        logger.info("SUCCESS: PRODUCTION SQLITE DATABASE SETUP COMPLETE!")
        logger.info("READY: READY FOR AUTONOMOUS TRADING!")
    else:
        logger.error("ERROR: PRODUCTION SQLITE DATABASE SETUP FAILED!")
        exit(1)
