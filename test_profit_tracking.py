#!/usr/bin/env python3
"""
Test script for hourly profit tracking functionality
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Setup
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
Path("logs").mkdir(exist_ok=True)

# Simple logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/test_profit_tracking.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("TestProfitTracking")

class MockTradingSystem:
    """Mock trading system to test profit tracking"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.last_hourly_report = datetime.now()
        self.total_profit = 0.0
        self.total_trades = 0
        
        # PROFIT TARGET SYSTEM - $15,000/DAY TARGET
        self.profit_targets = {
            'hourly_target': 625.0,     # $625/hour for $15,000/day
            'daily_target': 15000.0,    # $15,000/day AGGRESSIVE TARGET
            'session_target': 0.0,      # Will be calculated based on runtime
            'profit_per_minute': 10.42, # $10.42/minute for $625/hour
            'profit_per_second': 0.174, # $0.174/second for maximum precision
            'target_achievement_rate': 0.0,
            'performance_multiplier': 1.0
        }
        
        # HOURLY PROFIT TRACKING
        self.hourly_stats = {
            'current_hour_start': datetime.now(),
            'current_hour_profit': 0.0,
            'current_hour_trades': 0,
            'hourly_history': [],
            'best_hour_profit': 0.0,
            'worst_hour_profit': 0.0,
            'average_hourly_profit': 0.0,
            'hours_above_target': 0,
            'hours_below_target': 0
        }
        
        self.session_stats = {
            'start_balance': 1000.0,
            'current_balance': 1000.0,
            'total_profit': 0.0,
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'max_profit': 0.0,
            'max_loss': 0.0,
            'strategies_used': {},
            'symbols_traded': set(),
            'profit_velocity': 0.0,
            'target_performance': 0.0
        }
    
    async def generate_hourly_profit_summary(self):
        """Generate comprehensive hourly profit summary with target analysis"""
        try:
            current_time = datetime.now()
            runtime_hours = (current_time - self.start_time).total_seconds() / 3600
            runtime_minutes = (current_time - self.start_time).total_seconds() / 60
            
            # Calculate current session targets
            expected_profit_by_now = runtime_hours * self.profit_targets['hourly_target']
            self.profit_targets['session_target'] = expected_profit_by_now
            
            # Update target achievement rate
            if expected_profit_by_now > 0:
                self.profit_targets['target_achievement_rate'] = (self.total_profit / expected_profit_by_now) * 100
            
            # Calculate profit velocity
            if runtime_minutes > 0:
                self.session_stats['profit_velocity'] = self.total_profit / runtime_minutes
                self.session_stats['target_performance'] = (self.session_stats['profit_velocity'] / self.profit_targets['profit_per_minute']) * 100
            
            # Complete current hour stats
            hour_profit = self.hourly_stats['current_hour_profit']
            hour_trades = self.hourly_stats['current_hour_trades']
            
            # Update hourly records
            self.hourly_stats['hourly_history'].append({
                'hour_start': self.hourly_stats['current_hour_start'],
                'hour_end': current_time,
                'profit': hour_profit,
                'trades': hour_trades,
                'target_met': hour_profit >= self.profit_targets['hourly_target'],
                'performance_pct': (hour_profit / self.profit_targets['hourly_target']) * 100 if self.profit_targets['hourly_target'] > 0 else 0
            })
            
            # Update best/worst tracking
            if hour_profit > self.hourly_stats['best_hour_profit']:
                self.hourly_stats['best_hour_profit'] = hour_profit
            if hour_profit < self.hourly_stats['worst_hour_profit'] or self.hourly_stats['worst_hour_profit'] == 0:
                self.hourly_stats['worst_hour_profit'] = hour_profit
            
            # Update target achievement counters
            if hour_profit >= self.profit_targets['hourly_target']:
                self.hourly_stats['hours_above_target'] += 1
            else:
                self.hourly_stats['hours_below_target'] += 1
            
            # Calculate average hourly profit
            if len(self.hourly_stats['hourly_history']) > 0:
                total_hourly_profit = sum(h['profit'] for h in self.hourly_stats['hourly_history'])
                self.hourly_stats['average_hourly_profit'] = total_hourly_profit / len(self.hourly_stats['hourly_history'])
            
            # COMPREHENSIVE HOURLY REPORT
            print("\n" + "=" * 80)
            print("HOURLY PROFIT SUMMARY - ACTIVE TARGET TRACKING")
            print("=" * 80)
            print(f"Session Runtime: {runtime_hours:.2f} hours ({runtime_minutes:.1f} minutes)")
            print(f"Current Hour Completed: {self.hourly_stats['current_hour_start'].strftime('%H:%M')} - {current_time.strftime('%H:%M')}")
            print()
            
            print("PROFIT PERFORMANCE:")
            print(f"  This Hour Profit:     ${hour_profit:>10.2f}")
            print(f"  Hourly Target:        ${self.profit_targets['hourly_target']:>10.2f}")
            print(f"  Target Achievement:   {((hour_profit / self.profit_targets['hourly_target']) * 100):>9.1f}%")
            print(f"  Total Session Profit: ${self.total_profit:>10.2f}")
            print(f"  Expected by Now:      ${expected_profit_by_now:>10.2f}")
            print(f"  Overall Target Rate:  {self.profit_targets['target_achievement_rate']:>9.1f}%")
            print()
            
            print("VELOCITY ANALYSIS:")
            print(f"  Current Profit/Min:   ${self.session_stats['profit_velocity']:>10.3f}")
            print(f"  Target Profit/Min:    ${self.profit_targets['profit_per_minute']:>10.3f}")
            print(f"  Velocity Performance: {self.session_stats['target_performance']:>9.1f}%")
            print()
            
            print("HOURLY STATISTICS:")
            print(f"  Hours Above Target:   {self.hourly_stats['hours_above_target']:>10}")
            print(f"  Hours Below Target:   {self.hourly_stats['hours_below_target']:>10}")
            print(f"  Best Hour Profit:     ${self.hourly_stats['best_hour_profit']:>10.2f}")
            print(f"  Worst Hour Profit:    ${self.hourly_stats['worst_hour_profit']:>10.2f}")
            print(f"  Average Hour Profit:  ${self.hourly_stats['average_hourly_profit']:>10.2f}")
            print()
            
            print("TRADING ACTIVITY:")
            print(f"  This Hour Trades:     {hour_trades:>10}")
            print(f"  Total Session Trades: {self.total_trades:>10}")
            print(f"  Success Rate:         {(self.session_stats['successful_trades'] / max(self.total_trades, 1) * 100):>9.1f}%")
            print()
            
            # TARGET ACHIEVEMENT STATUS
            if hour_profit >= self.profit_targets['hourly_target']:
                status = "TARGET ACHIEVED"
                status_symbol = "SUCCESS"
            elif hour_profit >= self.profit_targets['hourly_target'] * 0.8:
                status = "NEAR TARGET"
                status_symbol = "WARNING"
            else:
                status = "BELOW TARGET"
                status_symbol = "ALERT"
            
            print(f"HOUR STATUS: {status_symbol} - {status}")
            print("=" * 80)
            
            # Reset for next hour
            self.hourly_stats['current_hour_start'] = current_time
            self.hourly_stats['current_hour_profit'] = 0.0
            self.hourly_stats['current_hour_trades'] = 0
            self.last_hourly_report = current_time
            
            return True
            
        except Exception as e:
            logger.error(f"Error generating hourly profit summary: {e}")
            return False
    
    def simulate_trade(self, profit: float):
        """Simulate a trade with given profit"""
        self.total_profit += profit
        self.total_trades += 1
        self.hourly_stats['current_hour_profit'] += profit
        self.hourly_stats['current_hour_trades'] += 1
        
        if profit > 0:
            self.session_stats['successful_trades'] += 1
        else:
            self.session_stats['failed_trades'] += 1
        
        print(f"TRADE EXECUTED: Profit=${profit:.2f}, Hour Total=${self.hourly_stats['current_hour_profit']:.2f}, Session Total=${self.total_profit:.2f}")

async def test_profit_tracking():
    """Test the profit tracking system"""
    print("TESTING HOURLY PROFIT TRACKING SYSTEM")
    print("=" * 50)
    
    system = MockTradingSystem()
    
    # Simulate some trades for $15,000/day target ($625/hour)
    print("\nSimulating trades for $15,000/day target...")
    system.simulate_trade(125.50)  # Large scalping profit
    system.simulate_trade(89.25)   # Arbitrage opportunity
    system.simulate_trade(-25.80)  # Small loss
    system.simulate_trade(245.75)  # Major momentum trade
    system.simulate_trade(156.30)  # Grid trading profit
    system.simulate_trade(78.90)   # Market making profit
    system.simulate_trade(-15.45)  # Minor loss
    system.simulate_trade(198.60)  # Volatility harvesting
    
    # Test hourly summary
    print("\nGenerating hourly profit summary...")
    success = await system.generate_hourly_profit_summary()
    
    if success:
        print("\nSUCCESS: Hourly profit tracking system working correctly!")
    else:
        print("\nERROR: Hourly profit tracking system failed!")
    
    return success

if __name__ == "__main__":
    asyncio.run(test_profit_tracking())
