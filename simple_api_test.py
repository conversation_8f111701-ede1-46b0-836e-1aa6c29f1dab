#!/usr/bin/env python3
"""
Simple API Test - Direct Bybit API call
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
import json
from urllib.parse import urlencode

# Your confirmed working credentials
API_KEY = "WbQDRvmESPfUGgXQEj"
API_SECRET = "vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga"

async def simple_api_test():
    """Simple direct API test"""
    print("SIMPLE BYBIT API TEST")
    print("=" * 40)
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Public endpoint (no auth)
        print("1. Testing public endpoint...")
        try:
            async with session.get("https://api.bybit.com/v5/market/time") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   SUCCESS: Server time = {data.get('result', {}).get('timeSecond', 'Unknown')}")
                else:
                    print(f"   FAILED: Status {response.status}")
                    return False
        except Exception as e:
            print(f"   ERROR: {e}")
            return False
        
        # Test 2: Private endpoint (with auth)
        print("2. Testing private endpoint...")
        try:
            timestamp = str(int(time.time() * 1000))
            recv_window = "5000"
            
            # Create query string
            params = [
                ('timestamp', timestamp),
                ('recv_window', recv_window)
            ]
            query_string = urlencode(params)
            
            # Create signature
            signing_string = f"{timestamp}{API_KEY}{recv_window}{query_string}"
            signature = hmac.new(
                API_SECRET.encode('utf-8'),
                signing_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Headers
            headers = {
                'X-BAPI-API-KEY': API_KEY,
                'X-BAPI-SIGN': signature,
                'X-BAPI-SIGN-TYPE': '2',
                'X-BAPI-TIMESTAMP': timestamp,
                'X-BAPI-RECV-WINDOW': recv_window,
                'Content-Type': 'application/json'
            }
            
            url = f"https://api.bybit.com/v5/account/wallet-balance?{query_string}"

            async with session.get(url, headers=headers) as response:
                data = await response.json()
                print(f"   Raw response: {data}")

                if response.status == 200:
                    if data.get('retCode') == 0:
                        print("   SUCCESS: Authentication working!")

                        # Show balance info
                        result = data.get('result', {})
                        if 'list' in result:
                            for wallet in result['list']:
                                account_type = wallet.get('accountType', 'Unknown')
                                total_equity = wallet.get('totalEquity', '0')
                                available = wallet.get('totalAvailableBalance', '0')
                                print(f"   Account: {account_type}")
                                print(f"   Total Equity: ${float(total_equity):.2f}")
                                print(f"   Available: ${float(available):.2f}")

                        return True
                    else:
                        print(f"   API Error: {data.get('retMsg', 'Unknown error')}")
                        print(f"   Code: {data.get('retCode', 'Unknown')}")

                        # Try alternative endpoint for account info
                        print("   Trying alternative account info endpoint...")
                        return await test_account_info(session, headers)
                else:
                    print(f"   HTTP Error: Status {response.status}")
                    return False
                    
        except Exception as e:
            print(f"   ERROR: {e}")
            return False

async def test_account_info(session, headers):
    """Test alternative account info endpoint"""
    try:
        # Try account info endpoint
        timestamp = str(int(time.time() * 1000))
        recv_window = "5000"

        params = [
            ('timestamp', timestamp),
            ('recv_window', recv_window)
        ]
        query_string = urlencode(params)

        # Update signature for new request
        signing_string = f"{timestamp}{API_KEY}{recv_window}{query_string}"
        signature = hmac.new(
            API_SECRET.encode('utf-8'),
            signing_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        headers['X-BAPI-SIGN'] = signature
        headers['X-BAPI-TIMESTAMP'] = timestamp

        url = f"https://api.bybit.com/v5/account/info?{query_string}"

        async with session.get(url, headers=headers) as response:
            data = await response.json()
            print(f"   Account info response: {data}")

            if response.status == 200 and data.get('retCode') == 0:
                print("   SUCCESS: Account info retrieved!")
                result = data.get('result', {})
                print(f"   Account details: {result}")
                return True
            else:
                print(f"   Account info failed: {data.get('retMsg', 'Unknown')}")
                return False

    except Exception as e:
        print(f"   Account info error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(simple_api_test())
    
    print("\n" + "=" * 40)
    if result:
        print("RESULT: API CREDENTIALS WORKING!")
        print("Your Bybit API is functional for live trading")
    else:
        print("RESULT: API TEST FAILED")
        print("There may be an issue with credentials or connection")
    print("=" * 40)
