"""
Terminal Diagnostic Script
Helps identify and fix VS Code terminal integration issues
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def diagnose_terminal_issues():
    """Diagnose terminal integration problems"""
    print("=== VS Code Terminal Diagnostic ===")
    
    # Check environment
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    
    # Check conda
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'None')
    conda_prefix = os.environ.get('CONDA_PREFIX', 'None')
    print(f"Conda environment: {conda_env}")
    print(f"Conda prefix: {conda_prefix}")
    
    # Check VS Code settings
    settings_path = Path(".vscode/settings.json")
    if settings_path.exists():
        print(f"VS Code settings found: {settings_path.absolute()}")
        try:
            with open(settings_path) as f:
                settings = json.load(f)
            
            # Check terminal settings
            terminal_profile = settings.get("terminal.integrated.defaultProfile.windows")
            print(f"Default terminal profile: {terminal_profile}")
            
            automation_profile = settings.get("terminal.integrated.automationProfile.windows")
            if automation_profile:
                print("Automation profile configured: YES")
                print(f"  Path: {automation_profile.get('path', 'Not set')}")
            else:
                print("Automation profile configured: NO")
                
        except Exception as e:
            print(f"Error reading settings: {e}")
    else:
        print("VS Code settings not found")
    
    # Test command execution
    print("\n=== Testing Command Execution ===")
    try:
        result = subprocess.run(['where', 'python'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"Python location: {result.stdout.strip()}")
        else:
            print("Python not found in PATH")
    except Exception as e:
        print(f"Error finding Python: {e}")
    
    try:
        result = subprocess.run(['where', 'conda'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"Conda location: {result.stdout.strip()}")
        else:
            print("Conda not found in PATH")
    except Exception as e:
        print(f"Error finding Conda: {e}")
    
    print("\n=== Environment Variables ===")
    relevant_vars = ['PATH', 'PYTHONPATH', 'CONDA_DEFAULT_ENV', 'CONDA_PREFIX']
    for var in relevant_vars:
        value = os.environ.get(var, 'Not set')
        if var == 'PATH':
            # Show only relevant parts of PATH
            paths = value.split(';')
            relevant_paths = [p for p in paths if 'conda' in p.lower() or 'python' in p.lower()]
            print(f"{var}: {len(paths)} total paths, {len(relevant_paths)} conda/python related")
            for path in relevant_paths[:5]:  # Show first 5 relevant
                print(f"  - {path}")
        else:
            print(f"{var}: {value}")
    
    print("\n=== Recommendations ===")
    print("1. Restart VS Code completely")
    print("2. Open a new terminal manually (Ctrl+Shift+`)")
    print("3. Check if conda environment activates automatically")
    print("4. Try running: python test_terminal_access.py")

if __name__ == "__main__":
    diagnose_terminal_issues()
