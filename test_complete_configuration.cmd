@echo off
echo ========================================
echo COMPLETE CONFIGURATION TEST SCRIPT
echo ========================================
echo Testing all updated configurations...
echo.

echo 1. Testing Node.js installation...
echo Node.js path: E:\The_real_deal_copy\Bybit_Bot\BOT\temp_node_extract\node-v20.11.0-win-x64
set "PATH=E:\The_real_deal_copy\Bybit_Bot\BOT\temp_node_extract\node-v20.11.0-win-x64;%PATH%"
node --version
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Node.js is working
) else (
    echo ERROR: Node.js failed
)
echo.

echo 2. Testing npm...
npm --version
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: npm is working
) else (
    echo ERROR: npm failed
)
echo.

echo 3. Testing conda environment...
echo Conda path: E:\conda\miniconda3\Scripts\conda.exe
E:\conda\miniconda3\Scripts\conda.exe info --envs
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Conda is accessible
) else (
    echo ERROR: Conda failed
)
echo.

echo 4. Testing Python in bybit-trader environment...
echo Python path: E:\conda\envs\bybit-trader\python.exe
E:\conda\envs\bybit-trader\python.exe --version
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Python in bybit-trader environment is working
) else (
    echo ERROR: Python in bybit-trader environment failed
)
echo.

echo 5. Testing workspace directory...
echo Current directory: %CD%
echo Expected: E:\The_real_deal_copy\Bybit_Bot\BOT
if "%CD%" == "E:\The_real_deal_copy\Bybit_Bot\BOT" (
    echo SUCCESS: In correct workspace directory
) else (
    echo WARNING: Not in expected workspace directory
)
echo.

echo 6. Testing main.py existence...
if exist "main.py" (
    echo SUCCESS: main.py found
) else (
    echo ERROR: main.py not found
)
echo.

echo 7. Testing package.json existence...
if exist "package.json" (
    echo SUCCESS: package.json found
) else (
    echo ERROR: package.json not found
)
echo.

echo 8. Testing node_modules directory...
if exist "node_modules" (
    echo SUCCESS: node_modules directory found
) else (
    echo ERROR: node_modules directory not found
)
echo.

echo 9. Testing .env file...
if exist ".env" (
    echo SUCCESS: .env file found
) else (
    echo WARNING: .env file not found
)
echo.

echo 10. Testing VSCode configuration files...
if exist ".vscode\settings.json" (
    echo SUCCESS: VSCode settings.json found
) else (
    echo ERROR: VSCode settings.json not found
)

if exist ".vscode\tasks.json" (
    echo SUCCESS: VSCode tasks.json found
) else (
    echo ERROR: VSCode tasks.json not found
)

if exist ".vscode\launch.json" (
    echo SUCCESS: VSCode launch.json found
) else (
    echo ERROR: VSCode launch.json not found
)
echo.

echo ========================================
echo CONFIGURATION TEST COMPLETE
echo ========================================
echo.
echo All major components have been tested.
echo Check the output above for any errors.
echo.
echo Next steps:
echo 1. Fix any errors shown above
echo 2. Test VSCode tasks using Ctrl+Shift+P -> Tasks: Run Task
echo 3. Test Python imports and trading bot functionality
echo 4. Verify all configurations are working as expected
echo.
pause
